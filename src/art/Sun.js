import { Drawable } from './Drawable.js';
import { Ellipse } from './PolygonForms.js';
import { Tools } from './Tools.js';

export class Sun extends Drawable {
    constructor(x, y, size = null) {
        super();
        
        // Set default margin for the sun layer
        this.margin = 0.1;
        
        // Store position
        this.x = x;
        this.y = y;
        
        // Generate random size if not provided
        this.size = size || 150 + Tools.randInt(150);
        
        // Create the main sun circle
        this.sunCircle = new Ellipse(this.x, this.y, this.size, this.size);
        
        // Set default style
        this.sunCircle.setStyle({
            strokeStyle: "#",
            fillStyle: "#fff"
        });
        

        

    }
    
    /**
     * Paint the sun
     */
    paint() {
        console.log("painting the sun");
        // Draw the main sun circle
        this.sunCircle.paint();
    }
    

    
    /**
     * Get the bounds of the sun
     * @returns {Object} Bounds object with minX, minY, maxX, maxY, width, height
     */
    getBounds() {
        const radius = this.size * 0.5;
        return {
            minX: this.x - radius,
            minY: this.y - radius,
            maxX: this.x + radius,
            maxY: this.y + radius,
            width: this.size,
            height: this.size
        };
    }
}
