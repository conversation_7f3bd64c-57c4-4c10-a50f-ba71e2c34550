"use strict";(()=>{var p="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";function B(){return`tz1${Array.from({length:33},()=>p[Math.random()*p.length|0]).join("")}`}function w(t){if(t.length!==36||!/^(tz|KT)[1-4]/.test(t))return!1;for(let r=0;r<t.length;r++)if(!p.includes(t[r]))return!1;return!0}function F(t){return/^(0x)?[0-9a-fA-F]{40}$/.test(t)}function v(){return`oo${Array.from({length:49},()=>p[Math.random()*p.length|0]).join("")}`}function M(t){return/^(0x)?([A-Fa-f0-9]{64})$/.test(t)}function X(t){return[...t].reduce(function(r,e){return r*p.length+p.indexOf(e)|0},0)}function q([t,r,e,n]){return function(){t|=0,r|=0,e|=0,n|=0;let a=(t+r|0)+n|0;return n=n+1|0,t=r^r>>>9,r=e+(e<<3)|0,e=e<<21|e>>>11,e=e+a|0,(a>>>0)/4294967296}}function I(t,r,e=X){let n=t.slice(r).match(new RegExp(".{"+(t.length-r>>2)+"}","g"));return n?n.map(e):[]}function W(t){return M(t)||F(t)?I(t,2,r=>Number(BigInt(`0x${r}`)%BigInt(4294967295))):w(t)?I(t,3):I(t,2)}function x(t){let r=W(t);return q(r)}function H(t){let r=t.indexOf("#");if(r===-1)return{params:"",lineage:[]};let e=t.slice(r+1);if(e.startsWith("0x"))return{params:e,lineage:[]};let n=new URLSearchParams(e),a=[],o=n.get("lineage");return o&&(a=o.split(",").map(u=>u.trim())),{params:n.get("params")||e,lineage:a}}function $(t){let r=t.replace("#","");return r.length===6&&(r=`${r}ff`),r.length===3&&(r=`${r[0]}${r[0]}${r[1]}${r[1]}${r[2]}${r[2]}ff`),r}var L=function(t){let r="";for(let e=0;e<t.length;e++)r+=t.charCodeAt(e).toString(16).padStart(4,"0");return r},J=function(t){let r=t.match(/.{1,4}/g)||[],e="";for(let n=0;n<r.length;n++){let a=parseInt(r[n],16);if(a===0)break;e+=String.fromCharCode(a)}return e},j=BigInt("-9223372036854775808"),O=BigInt("9223372036854775807"),l={number:{serialize:t=>{let r=new DataView(new ArrayBuffer(8));return r.setFloat64(0,t),r.getBigUint64(0).toString(16).padStart(16,"0")},deserialize:t=>{let r=new DataView(new ArrayBuffer(8));for(let e=0;e<8;e++)r.setUint8(e,parseInt(t.substring(e*2,e*2+2),16));return r.getFloat64(0)},bytesLength:()=>8,constrain:(t,r)=>{let e=Number.MIN_SAFE_INTEGER;typeof r.options?.min<"u"&&(e=Number(r.options.min));let n=Number.MAX_SAFE_INTEGER;typeof r.options?.max<"u"&&(n=Number(r.options.max)),n=Math.min(n,Number.MAX_SAFE_INTEGER),e=Math.max(e,Number.MIN_SAFE_INTEGER);let a=Math.min(Math.max(t,e),n);if(r?.options?.step){let o=1/r?.options?.step;return Math.round(a*o)/o}return a},random:t=>{let r=Number.MIN_SAFE_INTEGER;typeof t.options?.min<"u"&&(r=Number(t.options.min));let e=Number.MAX_SAFE_INTEGER;typeof t.options?.max<"u"&&(e=Number(t.options.max)),e=Math.min(e,Number.MAX_SAFE_INTEGER),r=Math.max(r,Number.MIN_SAFE_INTEGER);let n=Math.random()*(e-r)+r;if(t?.options?.step){let a=1/t?.options?.step;return Math.round(n*a)/a}return n}},bigint:{serialize:t=>{let r=new DataView(new ArrayBuffer(8));return r.setBigInt64(0,BigInt(t)),r.getBigUint64(0).toString(16).padStart(16,"0")},deserialize:t=>{let r=new DataView(new ArrayBuffer(8));for(let e=0;e<8;e++)r.setUint8(e,parseInt(t.substring(e*2,e*2+2),16));return r.getBigInt64(0)},bytesLength:()=>8,random:t=>{let r=j,e=O;typeof t.options?.min<"u"&&(r=BigInt(t.options.min)),typeof t.options?.max<"u"&&(e=BigInt(t.options.max));let n=e-r,a=n.toString(2).length,o;do o=BigInt("0b"+Array.from(crypto.getRandomValues(new Uint8Array(Math.ceil(a/8)))).map(u=>u.toString(2).padStart(8,"0")).join(""));while(o>n);return o+r}},boolean:{serialize:t=>typeof t=="boolean"?t?"01":"00":typeof t=="string"&&t==="true"?"01":"00",deserialize:t=>t!=="00",bytesLength:()=>1,random:()=>Math.random()<.5},color:{serialize:t=>$(t),deserialize:t=>t,bytesLength:()=>4,transform:t=>{let r=$(t),e=parseInt(r.slice(0,2),16),n=parseInt(r.slice(2,4),16),a=parseInt(r.slice(4,6),16),o=parseInt(r.slice(6,8),16);return{hex:{rgb:"#"+t.slice(0,6),rgba:"#"+t},obj:{rgb:{r:e,g:n,b:a},rgba:{r:e,g:n,b:a,a:o}},arr:{rgb:[e,n,a],rgba:[e,n,a,o]}}},constrain:t=>t.replace("#","").slice(0,8).padEnd(8,"f"),random:()=>`${[...Array(8)].map(()=>Math.floor(Math.random()*16).toString(16)).join("")}`},string:{serialize:(t,r)=>{if(!r.version){let a=L(t.substring(0,64));return a=a.padEnd(64*4,"0"),a}let e=64;typeof r.options?.maxLength<"u"&&(e=Number(r.options.maxLength));let n=L(t.substring(0,e));return n=n.padEnd(e*4,"0"),n},deserialize:t=>J(t),bytesLength:t=>t.version&&typeof t.options?.maxLength<"u"?Number(t.options.maxLength)*2:64*2,random:t=>{let r=0;typeof t.options?.minLength<"u"&&(r=t.options.minLength);let e=64;typeof t.options?.maxLength<"u"&&(e=t.options.maxLength);let n=Math.round(Math.random()*(e-r)+r);return[...Array(n)].map(a=>(~~(Math.random()*36)).toString(36)).join("")},constrain:(t,r)=>{let e=0;typeof r.options?.minLength<"u"&&(e=r.options.minLength);let n=64;typeof r.options?.maxLength<"u"&&(n=r.options.maxLength);let a=t.slice(0,n);return a.length<e?a.padEnd(e):a}},bytes:{serialize:(t,r)=>Array.from(t).map(e=>e.toString(16).padStart(2,"0")).join(""),deserialize:(t,r)=>{let e=t.length/2,n=new Uint8Array(e),a;for(let o=0;o<e;o++)a=o*2,n[o]=parseInt(`${t[a]}${t[a+1]}`,16);return n},bytesLength:t=>t.options.length,random:t=>{let r=t.options?.length||0,e=new Uint8Array(r);for(let n=0;n<r;n++)e[n]=Math.random()*255|0;return e}},select:{serialize:(t,r)=>Math.min(255,r.options?.options?.indexOf(t)||0).toString(16).padStart(2,"0"),deserialize:(t,r)=>{let e=parseInt(t,16);return r.options?.options?.[e]||r.options?.options?.[0]||""},bytesLength:()=>1,constrain:(t,r)=>r.options.options.includes(t)?t:r.options.options[0],random:t=>{let r=Math.round(Math.random()*(t.options.options.length-1)+0);return t?.options?.options[r]}}};function z(t,r){let e="";if(!r)return e;for(let n of r){let{id:a,type:o}=n,u=l[o],f=t[a],E=typeof f<"u"?f:typeof n.default<"u"?n.default:u.random(n),y=u.serialize(E,n);e+=y}return e}function N(t,r,e){let n={};for(let a of r){let o=l[a.type],u=e.withTransform&&o[e.transformType||"transform"];if(!t){let _;typeof a.default>"u"?_=o.random(a):_=a.default,n[a.id]=u?u(_,a):_;continue}let f=o.bytesLength(a),E=t.substring(0,f*2);t=t.substring(f*2);let y=o.deserialize(E,a);n[a.id]=u?u(y,a):y}return n}var V=(t,r,e,n)=>{let a=e.find(f=>f.id===t);if(!a)throw new Error(`No definition found for param ${t}`);let u=l[a.type][n];return u?.(r,a)||r},A=(t,r,e)=>{let n={};for(let a of r){let o=l[a.type],u=t[a.id],f=o[e];n[a.id]=f?.(u,a)||u}return n};var U="4.2.0";function C(t){let{parent:r}=t,e=new URLSearchParams(t.location.search),n=e.get("fxhash")||v(),a=e.get("fxminter")||B(),o=x(a),u=e.get("preview")==="1";function f(){t.dispatchEvent(new Event("fxhash-preview")),setTimeout(()=>f(),500)}function E(s=!1){t.dispatchEvent(new CustomEvent("fxhash-capture-frame",{detail:{isLastFrame:s}}))}let{params:y,lineage:_}=H(t.location.href),D=y.replace("0x",""),d=[..._,n],h=[...d.map(s=>x(s))],P=s=>{if(s<0||s>=d.length)throw new Error("Invalid depth");let i=d[s];h[s]=x(i),h[s].reset=()=>P(s),s===d.length-1&&(S=h[s],b.rand=S)};h.forEach((s,i)=>{s.reset=()=>P(i)});let S=h[d.length-1];function R(s){if(!h[s])throw new Error("Invalid depth");return h[s]()}R.reset=s=>{P(s)};let b={_version:U,_processors:l,_params:void 0,_features:void 0,_rawValues:{},_paramValues:{},_listeners:{},_receiveUpdateParams:async function(s,i){let m=await this._propagateEvent("params:update",s);m.forEach(([c,g])=>{typeof c=="boolean"&&!c||(this._updateParams(s),i?.()),g?.(c,s)}),m.length===0&&(this._updateParams(s),i?.())},_updateParams:function(s){if(!this._params)throw new Error("Params not defined");let i=A({...this._rawValues,...s},this._params,"constrain");Object.keys(i).forEach(m=>{this._rawValues[m]=i[m]}),this._paramValues=A(this._rawValues,this._params,"transform"),this._updateInputBytes()},_updateInputBytes:function(){if(!this._params)throw new Error("Params not defined");let s=z(this._rawValues,this._params);this.inputBytes=s},_emitParams:function(s){let i=Object.keys(s).reduce((m,c)=>{if(!this._params)throw new Error("Params not defined");return m[c]=V(c,s[c],this._params,"constrain"),m},{});this._receiveUpdateParams(i,()=>new Promise((m,c)=>{try{r.postMessage({id:"fxhash_emit:params:update",data:{params:i}},"*"),m()}catch(g){c(g)}}))},_fxRandByDepth:h,createFxRandom:x,hash:n,lineage:d,depth:d.length-1,rand:S,randAt:R,minter:a,randminter:o,iteration:Number(e.get("fxiteration"))||1,context:e.get("fxcontext")||"standalone",preview:f,captureFrame:E,isPreview:u,inputBytes:void 0,params:function(s){this._params=s.map(i=>({...i,version:this._version,value:i.default,options:i.options})),this._rawValues=N(D,this._params,{withTransform:!0,transformType:"constrain"}),this._paramValues=A(this._rawValues,this._params,"transform"),this._updateInputBytes()},features:function(s){this._features=s},getFeature:function(s){if(!this._features)throw new Error(`Feature ${s} not defined`);return this._features?.[s]},getFeatures:function(){return this._features||{}},getParam:function(s){return this._paramValues[s]},getParams:function(){return this._paramValues},getRawParam:function(s){return this._rawValues[s]},getRawParams:function(){return this._rawValues},getRandomParam:function(s){if(!this._params)throw new Error("Params not defined");let i=this._params.find(c=>c.id===s);if(!i)throw new Error(`Param with id ${s} not found`);return l[i.type].random(i)},getDefinitions:function(){return this._params?this._params:[]},stringifyParams:function(s){return JSON.stringify(s||this._rawValues,(i,m)=>typeof m=="bigint"?m.toString():m,2)},on:function(s,i,m){return this._listeners[s]||(this._listeners[s]=[]),this._listeners[s].push([i,m]),()=>{let c=this._listeners[s].findIndex(([g])=>g===i);c>-1&&this._listeners[s].splice(c,1)}},_propagateEvent:async function(s,i){let m=[];if(this._listeners?.[s])for(let[c,g]of this._listeners[s]){let T=c(i);m.push([T instanceof Promise?await T:T,g])}return m},emit:function(s,i){switch(s){case"params:update":this._emitParams(i);break;default:console.log("$fx.emit called with unknown id:",s);break}}},G=()=>{P(d.length-1)};S.reset=G;let k=()=>{o=x(a),b.randminter=o,o.reset=k};return o.reset=k,t.addEventListener("message",s=>{if(s.data==="fxhash_getInfo"&&r.postMessage({id:"fxhash_getInfo",data:{version:t.$fx._version,hash:t.$fx.hash,iteration:t.$fx.iteration,features:t.$fx.getFeatures(),params:{definitions:t.$fx.getDefinitions(),values:t.$fx.getRawParams()},minter:t.$fx.minter}},"*"),s.data?.id==="fxhash_params:update"){let{params:i}=s.data.data;i&&t.$fx._receiveUpdateParams(i)}}),b}window.$fx=C(window);})();
