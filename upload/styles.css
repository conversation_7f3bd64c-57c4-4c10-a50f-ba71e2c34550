body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    background-color: #000; /* Dark background to make the canvas stand out */
    overflow: hidden; /* Prevent scrollbars */
}

/* Container for positioning the canvas and overlay */
.canvas-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

canvas#canvas {
    border: none;
    margin: 0;
    width: auto;
    height: auto;
    max-width: 100vw;
    max-height: 100vh;
    object-fit: contain;
    display: block; /* Remove any extra space below the canvas */
}

/* City name overlay styling */
#city-name-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10; /* Ensure it's above the canvas */
    pointer-events: none; /* Allow clicks to pass through to the canvas */
    transition: all 0.8s ease-in-out; /* Smooth transition when repositioning */
}

/* Class for repositioned city name (bottom right, smaller) */


#city-name-overlay h1 {
    font-family: 'Arial', sans-serif; /* Simpler font */
    font-size: 3rem;
    font-weight: 400; /* Less bold for simplicity */
    color: rgba(0, 0, 0, 0.9); /* Dark text for better contrast */
    margin: 0;
    opacity: 0.8;
    padding: 0.2em 0.5em; /* Add padding around the text */
    letter-spacing: 0.03em; /* Slight letter spacing */
    text-transform: none; /* No text transform for simplicity */
    background-color: rgba(255, 255, 255, 1); /* Semi-transparent white background */
    border-radius: 1rem; /* Rounded corners */
    display: inline-block; /* Make background only as wide as the text */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
    transition: all 0.8s ease-in-out; /* Smooth transition when size changes */
}


/* Responsive adjustments */
@media (max-width: 768px) {
    #city-name-overlay h1 {
        font-size: 2.5rem;
        padding: 0.4em 0.8em; /* Slightly smaller padding */
        border-radius: 1.5rem; /* Slightly smaller border radius */
    }

    #city-name-overlay.corner-position h1 {
        font-size: 1.25rem; /* 50% smaller than the responsive size */
        background-color: rgba(255, 255, 255, 0.8); /* More opaque for better visibility on medium screens */
    }

    #city-name-overlay.corner-position {
        padding: 1rem; /* Smaller padding from edges on medium screens */
    }
}

@media (max-width: 480px) {
    #city-name-overlay h1 {
        font-size: 1.8rem;
        padding: 0.3em 0.6em; /* Even smaller padding */
        border-radius: 1rem; /* Even smaller border radius */
        background-color: rgba(255, 255, 255, 0.7); /* Slightly more opaque for better visibility on small screens */
    }

    #city-name-overlay.corner-position h1 {
        font-size: 0.9rem; /* 50% smaller than the responsive size */
        padding: 0.3em 0.6em; /* Smaller padding */
        background-color: rgba(255, 255, 255, 0.9); /* Most opaque for best visibility on small screens */
        border-radius: 0.7rem; /* Smaller border radius */
    }

    #city-name-overlay.corner-position {
        padding: 0.7rem; /* Minimal padding from edges on small screens */
    }
}