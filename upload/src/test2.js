import { Art } from './art/Art.js';
import { Scene } from './art/Scene.js';
import { Point } from './art/Point.js';
import { SwedishHouse, SwedishChurch } from './art/SwedishHouse.js';
import { Palette } from './art/Palette.js';
import { Canvas } from './art/Canvas.js';

// Initialize the scene
const canvas = document.getElementById('canvas');
if (!canvas) {
  console.error('Canvas not found! Make sure you have a canvas with id="canvas" in your HTML.');
} else {
  // Set up the art context
  Art.ctx = canvas.getContext('2d');
  Art.width=1920 
  Art.height=1080
  Art.grid_dist = 50;
  

  
  
let mycanvas=new Canvas(canvas)
 mycanvas.setRatio(1)

          

  
  // Create a scene with multiple layers
  const scene = new Scene();
  

    const house1 = new SwedishHouse(
      new Point(  200, 400),
      2 ,
      2 
    );

    const house2 = new SwedishHouse(
        new Point(  300, 400),
        4 ,
        2 
      );

      const house3 = new SwedishHouse(
        new Point(  500, 400),
        3 ,
        2 
      );
    scene.add(house1, 0);
  
    scene.add(house2, 0);
    scene.add(house3, 0);
  scene.paint()
}