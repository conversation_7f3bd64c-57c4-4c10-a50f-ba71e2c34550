import { Drawable } from './Drawable.js';
import { Art } from './Art.js';
import { Polygon } from './Polygon.js';
import { Point } from './Point.js';

export class SnowCap extends Drawable {
    constructor(layer) {
        super();
        this.layer = layer;
        this.points = [];
        
        // Get the layer's points to create the snow cap
        this.createSnowCap();
    }
    
    createSnowCap() {
        // Get the top points of the layer (the hill outline)
        const layerPoints = this.layer.points;
        
        // Find the highest and lowest y-coordinates in the layer
        let minY = Infinity;
        let maxY = -Infinity;
        
        for (const point of layerPoints) {
            minY = Math.min(minY, point.y);
            maxY = Math.max(maxY, point.y);
        }
        
        // Calculate the snow height (how far down the snow should go)
        // We'll make it cover about 15-25% of the hill height
        const hillHeight = maxY - minY;
        const snowHeight = hillHeight * (0.15 + Art.rand() * 0.1);
        const snowLine = minY + snowHeight;
        
        // Create points for the snow cap
        const snowPoints = [];
        
        // Find all points above the snow line
        for (let i = 0; i < layerPoints.length; i++) {
            const point = layerPoints[i];
            
            // If this point is above the snow line, add it to the snow cap
            if (point.y <= snowLine) {
                snowPoints.push(new Point(point.x, point.y));
            } 
            // If this point is below but the previous was above, add an interpolated point
            else if (i > 0 && layerPoints[i-1].y <= snowLine) {
                // Calculate the intersection point with the snow line
                const prevPoint = layerPoints[i-1];
                const t = (snowLine - prevPoint.y) / (point.y - prevPoint.y);
                const x = prevPoint.x + t * (point.x - prevPoint.x);
                
                snowPoints.push(new Point(x, snowLine));
            }
            // If this point is above but the previous was below, add an interpolated point
            else if (i > 0 && layerPoints[i-1].y > snowLine && point.y <= snowLine) {
                // Calculate the intersection point with the snow line
                const prevPoint = layerPoints[i-1];
                const t = (snowLine - prevPoint.y) / (point.y - prevPoint.y);
                const x = prevPoint.x + t * (point.x - prevPoint.x);
                
                snowPoints.push(new Point(x, snowLine));
                snowPoints.push(new Point(point.x, point.y));
            }
        }
        
        // If we have enough points, create the snow polygon
        if (snowPoints.length >= 3) {
            this.snowPolygon = new Polygon(snowPoints);
            
            // Set the style for the snow
            this.snowPolygon.setStyle({
                fillStyle: '#FFFFFF', // Pure white for snow
                strokeStyle: '#EEEEEE', // Very light gray for subtle outline
                lineWidth: 0.5
            });
        }
        
        // Add some snow details (small bumps and variations)
        this.createSnowDetails(snowPoints, snowLine);
    }
    
    createSnowDetails(snowPoints, snowLine) {
        // Only create details if we have a valid snow polygon
        if (!this.snowPolygon) return;
        
        this.snowDetails = [];
        
        // Find the leftmost and rightmost x-coordinates
        let minX = Infinity;
        let maxX = -Infinity;
        
        for (const point of snowPoints) {
            minX = Math.min(minX, point.x);
            maxX = Math.max(maxX, point.x);
        }
        
        // Create 3-5 small snow bumps along the snow line
        const bumpCount = 3 + Math.floor(Art.rand() * 3);
        const width = maxX - minX;
        
        for (let i = 0; i < bumpCount; i++) {
            // Position bumps evenly along the width with some randomness
            const x = minX + (width * (i + 0.5)) / bumpCount + (Art.rand() - 0.5) * width * 0.1;
            
            // Create a small snow bump
            const bumpWidth = width * (0.05 + Art.rand() * 0.05);
            const bumpHeight = bumpWidth * (0.5 + Art.rand() * 0.5);
            
            // Create points for a small triangular bump
            const bumpPoints = [
                new Point(x - bumpWidth/2, snowLine),
                new Point(x, snowLine - bumpHeight),
                new Point(x + bumpWidth/2, snowLine)
            ];
            
            const bump = new Polygon(bumpPoints);
            bump.setStyle({
                fillStyle: '#FFFFFF', // Pure white for snow
                strokeStyle: 'transparent' // No outline for the bumps
            });
            
            this.snowDetails.push(bump);
        }
    }
    
    paint() {
        // Paint the main snow cap
        if (this.snowPolygon) {
            this.snowPolygon.paint();
        }
        
        // Paint the snow details
        if (this.snowDetails) {
            for (const detail of this.snowDetails) {
                detail.paint();
            }
        }
    }
}
