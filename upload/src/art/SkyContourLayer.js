// Class for handling sky contour lines that follow a noise field
import { Drawable } from './Drawable.js';
import { Art } from './Art.js';
import { Point } from './Point.js';
import { Noise } from './Noise.js';

export class SkyContourLayer extends Drawable {
    constructor(width, height) {
        super();

        // Store dimensions
        this.width = width;
        this.height = height;
        
        // Set a margin for the sky layer
        this.margin = 0.1;
        
        // Create a noise instance for the contour lines
        this.noise = new Noise();
        
        // Default properties
        this.contourSpacing = 30; // Grid cell size for the noise field
        this.noiseScale = 0.002; // Scale factor for the noise field
        this.lineOpacity = 0.2; // Opacity for the contour lines
    }

    // Paint the sky contour lines
    paint() {
        // Save the current context state
        Art.ctx.save();

        // Get opacity from $fx parameter if available
        let opacity = this.lineOpacity;
        try {
            opacity = $fx.getParam("sky_contour_opacity");
        } catch (e) {
            // If $fx is not available or parameter is not defined, use default
            console.log("Using default sky contour opacity");
        }

        // Set the style for sky contour lines
        // Use a dark blue-gray color for better visibility against the sky
        Art.ctx.strokeStyle = `rgba(40, 50, 70, ${opacity})`;
        Art.ctx.lineWidth = 0.8 * Art.ratio; // Thinner lines for a more delicate effect

        // Draw the contour lines
        this.drawSkyContours();

        // Restore the context state
        Art.ctx.restore();
    }

    // Draw contour lines that follow a true noise field
    drawSkyContours() {
        // Get noise scale from $fx parameter if available
        let noiseScaleValue = this.noiseScale;
        try {
            noiseScaleValue = $fx.getParam("contour_noise_scale");
        } catch (e) {
            // If $fx is not available or parameter is not defined, use default
            console.log("Using default contour noise scale");
        }
        
        // Get density factor from $fx parameter if available
        let densityFactor = 1.0;
        try {
            densityFactor = $fx.getParam("contour_density");
        } catch (e) {
            // If $fx is not available or parameter is not defined, use default
            console.log("Using default contour density");
        }
        
        // Adjust grid size based on density factor (smaller = more detail)
        const gridSize = this.contourSpacing / densityFactor;
        
        // Create a grid of noise values covering the entire canvas
        const cols = Math.ceil(this.width / gridSize) + 1;
        const rows = Math.ceil(this.height / gridSize) + 1;
        
        // Create a 2D array to store noise values
        const noiseField = new Array(rows);
        for (let y = 0; y < rows; y++) {
            noiseField[y] = new Array(cols);
            for (let x = 0; x < cols; x++) {
                // Calculate the actual position in the canvas
                const posX = x * gridSize;
                const posY = y * gridSize;
                
                // Generate noise value at this position
                // Use multiple octaves of noise for more interesting patterns
                const nx = posX * noiseScaleValue;
                const ny = posY * noiseScaleValue;
                
                // Primary noise (large features)
                const n1 = this.noise.noise2D(nx, ny);
                
                // Secondary noise (medium details)
                const n2 = this.noise.noise2D(nx * 2, ny * 2) * 0.5;
                
                // Tertiary noise (small details)
                const n3 = this.noise.noise2D(nx * 4, ny * 4) * 0.25;
                
                // Combine noise values
                noiseField[y][x] = n1 + n2 + n3;
            }
        }
        
        // Define the contour levels (values at which to draw contour lines)
        // Create many closely spaced contour levels for dense lines
        const contourLevels = [];
        const contourStep = 0.05 / densityFactor; // Smaller step = more contour lines
        for (let level = -2; level <= 2; level += contourStep) {
            contourLevels.push(level);
        }
        
        // For each contour level, draw the corresponding contour lines
        for (const level of contourLevels) {
            // For each cell in the grid, check if a contour line passes through it
            for (let y = 0; y < rows - 1; y++) {
                for (let x = 0; x < cols - 1; x++) {
                    // Get the noise values at the four corners of this cell
                    const val1 = noiseField[y][x];
                    const val2 = noiseField[y][x + 1];
                    const val3 = noiseField[y + 1][x + 1];
                    const val4 = noiseField[y + 1][x];
                    
                    // Calculate the actual positions of the four corners
                    const x1 = x * gridSize;
                    const y1 = y * gridSize;
                    const x2 = (x + 1) * gridSize;
                    const y2 = (y + 1) * gridSize;
                    
                    // Check if the contour line passes through this cell
                    // by checking if the contour level is between any of the corner values
                    
                    // Interpolate to find where the contour line intersects the cell edges
                    const points = [];
                    
                    // Check top edge (between corners 1 and 2)
                    if ((val1 <= level && val2 >= level) || (val1 >= level && val2 <= level)) {
                        const t = (level - val1) / (val2 - val1);
                        points.push({ x: x1 + t * gridSize, y: y1 });
                    }
                    
                    // Check right edge (between corners 2 and 3)
                    if ((val2 <= level && val3 >= level) || (val2 >= level && val3 <= level)) {
                        const t = (level - val2) / (val3 - val2);
                        points.push({ x: x2, y: y1 + t * gridSize });
                    }
                    
                    // Check bottom edge (between corners 3 and 4)
                    if ((val3 <= level && val4 >= level) || (val3 >= level && val4 <= level)) {
                        const t = (level - val3) / (val4 - val3);
                        points.push({ x: x2 - t * gridSize, y: y2 });
                    }
                    
                    // Check left edge (between corners 4 and 1)
                    if ((val4 <= level && val1 >= level) || (val4 >= level && val1 <= level)) {
                        const t = (level - val4) / (val1 - val4);
                        points.push({ x: x1, y: y2 - t * gridSize });
                    }
                    
                    // If we found exactly 2 intersection points, draw a line between them
                    if (points.length === 2) {
                        Art.ctx.beginPath();
                        Art.ctx.moveTo(points[0].x, points[0].y);
                        Art.ctx.lineTo(points[1].x, points[1].y);
                        Art.ctx.stroke();
                    }
                }
            }
        }
    }

    // Get the bounds of the sky layer
    getBounds() {
        return {
            minX: 0,
            minY: 0,
            maxX: this.width,
            maxY: this.height,
            width: this.width,
            height: this.height
        };
    }
}
