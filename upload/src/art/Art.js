
export class Art {
    // static const ratio=1
    static ratio=1
    static dist=10
    static grid_dist=50*0.25
    static TAU=Math.PI*2
    static ctx=null
    static palette=null

    static RIGHT=0
    static DOWN=Math.PI/2
    static LEFT=Math.PI
    static UP=3*Math.PI/2
    
    static style={
        lineWidth:4*Art.ratio,
        fillStyle:"white",
        strokeStyle:"black",

    }
    static old_style=Art.style


    static rand(){
        //return Art.rand()
        return $fx.rand()

    }

    static randInt(num){
        return Math.floor(Art.rand()*num)
    }
    
    static setCtx(ctx){
        Art.old_ctx=Art.ctx
        Art.ctx=ctx
    }

    static setLineWidth(width){
        Art.ctx.lineWidth=width*Art.ratio
    }   

    static restoreCtx(){
        Art.ctx=Art.old_ctx
    }


    static moveTo(x,y){
        Art.ctx.moveTo(x*Art.ratio,y*Art.ratio)
    }

    static lineTo(x,y){
        Art.ctx.lineTo(x*Art.ratio,y*Art.ratio)
    }

    static fillRect(x,y,width,height){
        Art.ctx.fillRect(x*Art.ratio,y*Art.ratio,width*Art.ratio,height*Art.ratio)
    }

    static clearRect(x,y,width,height){
        Art.ctx.clearRect(x*Art.ratio,y*Art.ratio,width*Art.ratio,height*Art.ratio)
    }

    static setPalette(palette){
        Art.palette=palette
    }

    /*
    static getColor(index){
        return Art.palette.getColor(index)
    }*/

    static applyStyle(){
        Art.ctx.lineWidth=Art.style.lineWidth
        Art.ctx.fillStyle=Art.style.fillStyle
        Art.ctx.strokeStyle=Art.style.strokeStyle
    }

    static setStyle(style){
        Art.old_style = { ...Art.style };
        Art.style = { ...Art.style, ...style };
        Art.applyStyle();
    }

    static restoreStyle(){
        Art.style=Art.old_style
        Art.applyStyle()
    }
 }
 