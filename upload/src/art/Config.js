export class Config {
    constructor() {
        this.data = {};
        this.isLoaded = false;
    }

    /**
     * Load configuration from a JSON file
     * @param {string} path - Path to the JSON file
     * @returns {Promise<Config>} - Returns this instance for chaining
     */
    async load(path) {
        try {
            const response = await fetch(path);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.data = await response.json();
            this.isLoaded = true;
            console.log('Config loaded successfully', this.data);
        } catch (error) {
            console.error(`Error loading config} path `+path);
        }

        return this;
    }

    get(key,def) {
        if(this.data[key]==undefined) return def;
        return this.data[key];
    }

    // ... rest of your methods remain the same
}