import { Drawable } from './Drawable.js';
import { Art } from './Art.js';
import { Sun } from './Sun.js';

export class SunLayer extends Drawable {
    constructor(width, height) {
        super();

        // Set a margin for the sun layer to allow for movement
        this.margin = 0.3;

        // Store canvas dimensions
        this.width = width;
        this.height = height;

        // Create the sun
        // Position the sun more centrally to avoid it moving off-screen
        const sunX = width*(0.5+0.5*Art.rand()-0.25);
        const sunY = height*(0.25+0.25*Art.rand()-0.125);
        this.sun = new Sun(sunX, sunY);

        // Store time of creation for animation
        this.creationTime = Date.now();
    }

    /**
     * Paint only the sun (no background)
     */
    paint() {
        // Save current context state
        Art.ctx.save();

        // Draw the sun
        this.sun.paint();

        // Restore context state
        Art.ctx.restore();
    }

    /**
     * Get the bounds of the sun layer
     * @returns {Object} Bounds object with minX, minY, maxX, maxY, width, height
     */
    getBounds() {
        // Use the sun's bounds plus some margin
        const sunBounds = this.sun.getBounds();
        
        // Add some margin around the sun's bounds
        return {
            minX: sunBounds.minX - this.width * 0.1,
            minY: sunBounds.minY - this.height * 0.1,
            maxX: sunBounds.maxX + this.width * 0.1,
            maxY: sunBounds.maxY + this.height * 0.1,
            width: sunBounds.width + this.width * 0.2,
            height: sunBounds.height + this.height * 0.2
        };
    }
}
