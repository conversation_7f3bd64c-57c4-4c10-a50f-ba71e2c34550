// Class for handling paths
import {Drawable} from './Drawable.js'
import {Point} from './Point.js'

import {Art} from './Art.js'

export class Path extends Drawable {
  isPolygon = false;

    constructor(points = []) {
      super();
      this.points = points.map(point => 
        point instanceof Point ? point : new Point(point.x, point.y)
      );
    }
  
    addPoint(point) {
      this.points.push(point instanceof Point ? point : new Point(point.x, point.y));
      return this;
    }
  
    clone(path) {
      this.points = path.points.map(point => new Point(point.x, point.y));
      return this;
    }

    static getPolarLine(p, angle, distance, turn=0){
        let dist=0 
        let path=new Path()
        path.addPoint(p)
        let num=distance/Art.dist
        let angleStep=turn/num

        while(dist<distance){
          p=p.movePolar(angle,Art.dist)
          path.addPoint(p)
          dist+=Art.dist
          angle+=angleStep
        }
        return path
    }

    getTangentAngle(indx) {
      let p1, p2;
      if (indx === 0) {
          p1 = this.points[indx];
          p2 = this.points[indx + 1];
      } else if (indx === this.points.length - 1) {
          p1 = this.points[indx - 1];
          p2 = this.points[indx];
      } else {
          p1 = this.points[indx - 1];
          p2 = this.points[indx + 1];
      }
      return Math.atan2(p2.y - p1.y, p2.x - p1.x);
  }

    
    //creates a path from a point and a condition and a turn function
    static getPolarLineCondition(p, fTurn, fCondition) {
      // Explicitly create a new Path instance
      let path = new Path()
      path.addPoint(p)
      let c = 0
      
      while(true) {
        // Calculate the next point
        let nextPoint = p.movePolar(fTurn(p), Art.dist)
        
        // Check if the next point would exceed the condition
        if(!fCondition(nextPoint)) {
          break
        }
        
        // If not, update p to the next point and add it to the path
        p = nextPoint
        path.addPoint(p)
        
        c++
        if(c > 1000) {
          console.error("Infinite loop")
          break
        }
      }
      
      // Ensure the returned object is a proper Path instance
      console.log("Returning path:", path);
      console.log("Path is Path instance:", path instanceof Path);
      console.log("Path has getTangentAngle:", typeof path.getTangentAngle === 'function');
      
      return path
    }

  getLastPoint(){
    return this.points[this.points.length-1]
  }
  
    getLength() {
      let length = 0;
      for (let i = 1; i < this.points.length; i++) {
        length += this.points[i].distanceTo(this.points[i - 1]);
      }
      return length;
    }
  
    getPointAt(distance) {
      let currentDist = 0;
      
      for (let i = 1; i < this.points.length; i++) {
        const segmentLength = this.points[i].distanceTo(this.points[i - 1]);
        
        if (currentDist + segmentLength >= distance) {
          const t = (distance - currentDist) / segmentLength;
          const x = this.points[i - 1].x + (this.points[i].x - this.points[i - 1].x) * t;
          const y = this.points[i - 1].y + (this.points[i].y - this.points[i - 1].y) * t;
          return new Point(x, y);
        }
        
        currentDist += segmentLength;
      }
      
      return this.points[this.points.length - 1].clone();
    }
  
    trace() {
      if (this.points.length < 2) return;
  
     // this.style.apply(ctx);
     
      Art.ctx.beginPath();
      Art.moveTo(this.points[0].x, this.points[0].y);
      
      for (let i = 1; i < this.points.length; i++) {
        Art.lineTo(this.points[i].x, this.points[i].y);
      }
      // Only close the path if it's a polygon
      if (this.isPolygon) {
        Art.lineTo(this.points[0].x, this.points[0].y);
      }

     // ctx.stroke();
 
    }
    
    stroke(){
     
      this.style.apply();
      this.trace()
      Art.ctx.stroke();
    }


    move(x,y){
      this.points=this.points.map(p=>p.move(x,y))
    }



        /**
     * Creates a polygon by extruding a path with varying width
     * @param {Path} path - The path to extrude
     * @param {number} width - Base width of the extrusion in pixels
     * @param {Array} levels - Array of multipliers at different points along the path (0-1)
     * @returns {Polygon} A new polygon representing the extruded path
     */
    static extrude(path, width, levels = [1, 1]) {
      // Import Polygon if not already available in this scope
       const { Polygon } = require('./Polygon.js');
      
      if (path.points.length < 2) {
        console.error("Cannot extrude a path with fewer than 2 points");
        return new Polygon([]);
      }
      
      const leftSide = [];
      const rightSide = [];
      
      // Calculate the total path length for interpolation
      const totalLength = path.getLength();
      let accumulatedLength = 0;
      
      // Process each segment of the path
      for (let i = 0; i < path.points.length - 1; i++) {
        const p1 = path.points[i];
        const p2 = path.points[i + 1];
        
        // Calculate segment direction vector
        const dx = p2.x - p1.x;
        const dy = p2.y - p1.y;
        const segmentLength = Math.sqrt(dx * dx + dy * dy);
        
        // Calculate normalized perpendicular vector
        const nx = -dy / segmentLength;
        const ny = dx / segmentLength;
        
        // Calculate relative position along the path (0 to 1)
        const posStart = accumulatedLength / totalLength;
        accumulatedLength += segmentLength;
        const posEnd = accumulatedLength / totalLength;
        
        // Interpolate width multiplier at this position
        const startMultiplier = Path.interpolateMultiplier(posStart, levels);
        const endMultiplier = Path.interpolateMultiplier(posEnd, levels);
        
        // Create the extruded points for this segment
        const halfWidthStart = (width * startMultiplier) / 2;
        const halfWidthEnd = (width * endMultiplier) / 2;
        
        // First point of the segment
        if (i === 0) {
          leftSide.push(new Point(
            p1.x + nx * halfWidthStart,
            p1.y + ny * halfWidthStart
          ));
          
          rightSide.push(new Point(
            p1.x - nx * halfWidthStart,
            p1.y - ny * halfWidthStart
          ));
        }
        
        // Second point of the segment
        leftSide.push(new Point(
          p2.x + nx * halfWidthEnd,
          p2.y + ny * halfWidthEnd
        ));
        
        rightSide.push(new Point(
          p2.x - nx * halfWidthEnd,
          p2.y - ny * halfWidthEnd
        ));
      }
      
      // Combine the left and right sides to form a closed polygon
      // The right side points need to be in reverse order
      const polygonPoints = [...leftSide, ...rightSide.reverse()];
      
      return new Polygon(polygonPoints);
    }
    
    /**
     * Helper method to interpolate width multiplier at a specific position
     * @param {number} position - Position along the path (0-1)
     * @param {Array} levels - Array of width multipliers
     * @returns {number} Interpolated width multiplier
     */
    static interpolateMultiplier(position, levels) {
      if (levels.length === 1) return levels[0];
      
      // Map position (0-1) to levels array
      const index = position * (levels.length - 1);
      const lowerIndex = Math.floor(index);
      const upperIndex = Math.ceil(index);
      
      // Handle edge cases
      if (lowerIndex === upperIndex) return levels[lowerIndex];
      
      // Linear interpolation between the two nearest levels
      const t = index - lowerIndex;
      return levels[lowerIndex] * (1 - t) + levels[upperIndex] * t;
    }
    debug(){
      //todo , ha de ser un canvi temporal d'estil
      this.style.strokeStyle="red"
      this.style.apply()
      this.trace()
      Art.ctx.stroke()
    }

  }

