import {Art} from './Art.js'

export class Style {
    constructor({
      strokeStyle = '#404757',
      fillStyle = '#385E6F',
      lineWidth = 4*Art.ratio,
      lineCap = 'butt',
      lineJoin = 'miter'
    } = {}) {
      this.strokeStyle = strokeStyle;
      this.fillStyle = fillStyle;
      this.lineWidth = lineWidth;
      this.lineCap = lineCap;
      this.lineJoin = lineJoin;
    }
    
    setLineWidth(width) {
      this.lineWidth = width*Art.ratio;
      return this;
    }

    apply(customCtx) {
      // Use provided context or fall back to Art.ctx
      const ctx = customCtx || Art.ctx;
      
      if (!ctx) {
        console.error("No context available for Style.apply()");
        return;
      }
      
      const { strokeStyle, fillStyle, lineWidth, lineCap, lineJoin } = this;
      Object.assign(ctx, { strokeStyle, fillStyle, lineWidth, lineCap, lineJoin });
    }

      clone() {
      return new Style({
        strokeStyle: this.strokeStyle,
        fillStyle: this.fillStyle,
        lineWidth: this.lineWidth,
        lineCap: this.lineCap,
        lineJoin: this.lineJoin
      });
    }

  }