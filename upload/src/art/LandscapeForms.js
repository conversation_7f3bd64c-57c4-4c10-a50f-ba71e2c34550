import { Drawable } from "./Drawable.js";
import { Art } from "./Art.js";
import { Tools } from "./Tools.js";

export class LandscapeGround extends Drawable{
    constructor(point, gridd_w) {
        super();
        this.point=point
        this.gridd_w=gridd_w
       
    }

    paint(){    
        this.setStyle({
            lineWidth: 0.15,
            strokeStyle: Art.palette.getColor("landscape"),
           

        })
       //this.style.apply();
        Art.ctx.beginPath()
        Art.moveTo(this.point.x, this.point.y)
        Art.lineTo(
            this.point.x + this.gridd_w * Art.grid_dist, 
            this.point.y)
       // Art.lineTo(this.point.x + this.gridd_w*2, this.point.y)
        Art.ctx.stroke()
       
    }
}


export class LandscapeSnow extends Drawable{
    constructor(point, gridd_w) {
        super();
        this.point = point;
        this.point=this.point.move(-Art.grid_dist*0.5,0)
        this.gridd_w = gridd_w; //todo corregir
        // Random height for the snow pile - between 10% and 30% of grid distance
        
        // Store the current grid_dist at construction time to ensure perspective scaling
        this.currentGridDist = Art.grid_dist;
        
        //param fins 4
        this.snowHeight =  0.25 * this.currentGridDist + 0.1 * this.currentGridDist;
        // Random width factor to vary the snow pile width
        this.widthFactor = 0.5 +  0.5; // Between 50-100% of grid width
    }

    paint() {
        this.setStyle({
            lineWidth: 1,
            strokeStyle:  "#000",
            fillStyle:  "#fff"
        });
        
        const startX = this.point.x;
        const endX = this.point.x + (this.gridd_w * this.currentGridDist * this.widthFactor);
        const midX = (startX + endX) / 2;
        const baseY = this.point.y;
        const peakY = baseY - this.snowHeight;
        
        // Start drawing the snow pile
        Art.ctx.beginPath();
        
        // Start at the left base point
        Art.moveTo(startX, baseY);
        
        // Create the curved top of the snow pile using quadratic curves
        // Left side curve
        Art.ctx.quadraticCurveTo(
            ((startX + midX) / 2) * Art.ratio, 
            (baseY - this.snowHeight * 0.8) * Art.ratio, 
            midX * Art.ratio, 
            peakY * Art.ratio
        );
        
        // Right side curve
        Art.ctx.quadraticCurveTo(
            ((midX + endX) / 2) * Art.ratio, 
            (baseY - this.snowHeight * 0.8) * Art.ratio, 
            endX * Art.ratio, 
            baseY * Art.ratio
        );
        
        // Close the path to create a complete shape
        Art.ctx.closePath();
        
        // Fill and stroke the snow pile
        Art.ctx.fill();
        Art.ctx.stroke();
        
        // Add some details to make it look more like snow
        this.addSnowDetails(startX, endX, baseY, peakY);
    }
    
    addSnowDetails(startX, endX, baseY, peakY) {
        // Add some small curves or dots to give texture to the snow
        // Only if the snow pile is large enough
        if (this.snowHeight > 0.15 * this.currentGridDist) {
            const width = endX - startX;
            
            // Set a lighter stroke for details
            Art.ctx.strokeStyle = Art.palette.getColor("snow_detail") || "rgba(220, 220, 220, 0.5)";
            Art.ctx.lineWidth = 0.2;
            
            // Add a few subtle curves on the surface
            const detailCount = Math.floor(2 + Art.rand() * 3); // 2-4 details
            
            for (let i = 0; i < detailCount; i++) {
                const detailX = startX + (width * (i + 1)) / (detailCount + 1);
                const detailY = baseY - this.snowHeight * (0.4 + Art.rand() * 0.4);
                
                Art.ctx.beginPath();
                Art.moveTo(detailX - width * 0.05, detailY + this.snowHeight * 0.1);
                Art.ctx.quadraticCurveTo(
                    detailX*Art.ratio, 
                    (detailY - this.snowHeight * 0.05)**Art.ratio, 
                    (detailX + width * 0.05)**Art.ratio, 
                    (detailY + this.snowHeight * 0.1)*Art.ratio
                );
                Art.ctx.stroke();
            }
        }
    }
}

