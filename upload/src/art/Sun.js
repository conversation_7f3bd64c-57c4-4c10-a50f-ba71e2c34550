import { Drawable } from './Drawable.js';
import { Art } from './Art.js';
import { Ellipse } from './PolygonForms.js';
import { Tools } from './Tools.js';

export class Sun extends Drawable {
    constructor(x, y, size = null) {
        super();
        
        // Set default margin for the sun layer
        this.margin = 0.1;
        
        // Store position
        this.x = x;
        this.y = y;
        
        // Generate random size if not provided
        this.size = size || 150 + Tools.randInt(150);
        
        // Create the main sun circle
        this.sunCircle = new Ellipse(this.x, this.y, this.size, this.size);
        
        // Set default style
        this.sunCircle.setStyle({
            strokeStyle: "#",
            fillStyle: "#fff"
        });
        
        // Create a glow effect
        this.glowSize = this.size * 1.2;
        this.glowOpacity = 0.3;
        
        // Store time of creation for animation
        this.creationTime = Date.now();
    }
    
    /**
     * Paint the sun with glow effect
     */
    paint() {
        console.log("painting the sun");
        // Save current context state
        Art.ctx.save();
        
        // Calculate animation values
        const time = (Date.now() - this.creationTime) / 1000; // Time in seconds
        const pulseFactor = 1 + Math.sin(time * 0.5) * 0.05; // Subtle pulsing effect
        
        // Draw outer glow
        const gradient = Art.ctx.createRadialGradient(
            this.x, this.y, this.size * 0.5,
            this.x, this.y, this.glowSize * pulseFactor
        );
        
        gradient.addColorStop(0, 'rgba(255, 255, 200, ' + this.glowOpacity + ')');
        gradient.addColorStop(0.5, 'rgba(255, 200, 100, ' + (this.glowOpacity * 0.5) + ')');
        gradient.addColorStop(1, 'rgba(255, 150, 50, 0)');
        
        Art.ctx.beginPath();
        Art.ctx.arc(this.x, this.y, this.glowSize * pulseFactor, 0, Math.PI * 2);
        Art.ctx.fillStyle = gradient;
        Art.ctx.fill();
        
        // Draw sun rays
        this.drawSunRays(time);
        
        // Draw the main sun circle
        this.sunCircle.paint();
        
        // Restore context state
        Art.ctx.restore();
    }
    
    /**
     * Draw animated sun rays
     * @param {number} time - Current time in seconds
     */
    drawSunRays(time) {
        const rayCount = 12;
        const rayLength = this.size * 0.5;
        const rayWidth = this.size * 0.05;
        
        Art.ctx.save();
        Art.ctx.translate(this.x, this.y);
        Art.ctx.rotate(time * 0.1); // Slow rotation
        
        // Draw rays
        for (let i = 0; i < rayCount; i++) {
            const angle = (i / rayCount) * Math.PI * 2;
            const rayExtension = Math.sin(time * 0.5 + i) * 0.2; // Ray length variation
            
            Art.ctx.save();
            Art.ctx.rotate(angle);
            
            // Create ray gradient
            const rayGradient = Art.ctx.createLinearGradient(
                this.size * 0.5, 0,
                this.size * 0.5 + rayLength * (1 + rayExtension), 0
            );
            rayGradient.addColorStop(0, 'rgba(255, 255, 200, 0.8)');
            rayGradient.addColorStop(1, 'rgba(255, 200, 100, 0)');
            
            // Draw ray
            Art.ctx.beginPath();
            Art.ctx.moveTo(this.size * 0.5, -rayWidth);
            Art.ctx.lineTo(this.size * 0.5 + rayLength * (1 + rayExtension), 0);
            Art.ctx.lineTo(this.size * 0.5, rayWidth);
            Art.ctx.fillStyle = rayGradient;
            Art.ctx.fill();
            
            Art.ctx.restore();
        }
        
        Art.ctx.restore();
    }
    
    /**
     * Get the bounds of the sun including its glow
     * @returns {Object} Bounds object with minX, minY, maxX, maxY, width, height
     */
    getBounds() {
        const totalSize = this.glowSize * 1.5; // Include rays in bounds calculation
        return {
            minX: this.x - totalSize,
            minY: this.y - totalSize,
            maxX: this.x + totalSize,
            maxY: this.y + totalSize,
            width: totalSize * 2,
            height: totalSize * 2
        };
    }
}
