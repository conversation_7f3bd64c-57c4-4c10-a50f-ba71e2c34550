// Class for handling paths
import {Drawable} from './Drawable.js'

import {Art} from './Art.js'
import {Point} from './Point.js'
import { Path } from './Path.js';

//it's a path closed
export class Polygon extends Path {
    isPolygon = true;


    constructor(points = []) {
      super();
      this.points = points.map(point => 
        point instanceof Point ? point : new Point(point.x, point.y)
      );
      
    }

    getPoint(num){
        return this.points[num]
    }
    
    width(){
        let minx=100000
        let maxx=-100000
        this.points.forEach(p=>{
            minx=Math.min(minx,p.x)
            maxx=Math.max(maxx,p.x)
        })
        return maxx-minx
    }

    height(){
        let miny=100000
        let maxy=-100000
        this.points.forEach(p=>{
            miny=Math.min(miny,p.y)
            maxy=Math.max(maxy,p.y)
        })
        return maxy-miny
    }
    fill(){
        this.style.apply()
        this.trace()
        Art.ctx.fill()
    }
    paint(){
        this.stroke()
        this.fill()
    }


    getYAtX(x) {
        let points = this.points;
    
        for (let i = 0; i < points.length - 1; i++) {
            let p1 = points[i];
            let p2 = points[i + 1];
    
            // Comprovar si x està entre p1.x i p2.x
            if (p1.x <= x && x <= p2.x) {
                // Interpolació lineal per trobar y
                let t = (x - p1.x) / (p2.x - p1.x);
                return p1.y + t * (p2.y - p1.y);
            }
        }
    
        return null; // Fora dels límits de la capa
    }

 

    flipHorizontal() {
        // Get the left and bottom bounds before flipping
        let leftX = Math.min(...this.points.map(p => p.x));
        let bottomY = Math.max(...this.points.map(p => p.y));
        
        // Calculate width to determine the right bound
        let rightX = Math.max(...this.points.map(p => p.x));
        let width = rightX - leftX;
        
        // Flip points horizontally around their own center
        const centerX = (leftX + rightX) / 2;
        this.points = this.points.map(point => new Point(2 * centerX - point.x, point.y));
        
        // Get the new left bound after flipping
        let newLeftX = Math.min(...this.points.map(p => p.x));
        
        // Calculate the adjustment needed to maintain the original left position
        let adjustX = leftX - newLeftX;
        
        // Adjust all points to maintain the original left bottom position
        this.points = this.points.map(point => new Point(point.x + adjustX, point.y));
    }
    
    containsPoint(point) {
        let inside = false;
        let points = this.points;
        let j = points.length - 1;
    
        for (let i = 0; i < points.length; i++) {
            let xi = points[i].x, yi = points[i].y;
            let xj = points[j].x, yj = points[j].y;
    
            let intersect = ((yi > point.y) !== (yj > point.y)) &&
                            (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);
            if (intersect) inside = !inside;
            j = i;
        }
        return inside;
    }
    
    getBounds() {
        const xCoords = this.points.map(p => p.x);
        const yCoords = this.points.map(p => p.y);
        return {
            minX: Math.min(...xCoords),
            maxX: Math.max(...xCoords),
            minY: Math.min(...yCoords),
            maxY: Math.max(...yCoords),
            width: Math.max(...xCoords) - Math.min(...xCoords),
            height: Math.max(...yCoords) - Math.min(...yCoords)
        };
    }

    /*

    clone() {
        const newPolygon = new Polygon();
        // Clone all points
        newPolygon.points = this.points.map(point => new Point(point.x, point.y));
        
        // Clone style if it exists
        if (this.style) {
            newPolygon.style = Object.assign({}, this.style);
        }else{
            newPolygon.style=new Style()
        }
        
        return newPolygon;
    }*/

}

// This class has been moved to MultiPolygon.js