import argparse
import sys
import time
from functools import lru_cache
from collections import deque

import cv2
import numpy as np

from picamera2 import MappedArray, Picamera2
from picamera2.devices import IMX500
from picamera2.devices.imx500 import (NetworkIntrinsics,
                                      postprocess_nanodet_detection)

# For storing the last detection results
last_detections = []
# For storing the processed position data
position_data = {"x": 0, "y": 0, "num_people": 0}

# For position smoothing
position_history = {
    "x": deque(maxlen=5),  # Store last 5 x values
    "y": deque(maxlen=5)   # Store last 5 y values
}

# For tracking detection persistence
last_detection_time = 0
position_hold_time = 1.0  # seconds to hold last position when detection fails


class Detection:
    def __init__(self, coords, category, conf, metadata):
        """Create a Detection object, recording the bounding box, category and confidence."""
        self.category = category
        self.conf = conf
        self.box = imx500.convert_inference_coords(coords, metadata, picam2)


def parse_detections(metadata: dict):
    """Parse the output tensor into a number of detected objects, scaled to the ISP output."""
    global last_detections
    bbox_normalization = intrinsics.bbox_normalization
    bbox_order = intrinsics.bbox_order
    threshold = args.threshold
    iou = args.iou
    max_detections = args.max_detections

    np_outputs = imx500.get_outputs(metadata, add_batch=True)
    input_w, input_h = imx500.get_input_size()
    if np_outputs is None:
        return last_detections
    if intrinsics.postprocess == "nanodet":
        boxes, scores, classes = \
            postprocess_nanodet_detection(outputs=np_outputs[0], conf=threshold, iou_thres=iou,
                                          max_out_dets=max_detections)[0]
        from picamera2.devices.imx500.postprocess import scale_boxes
        boxes = scale_boxes(boxes, 1, 1, input_h, input_w, False, False)
    else:
        boxes, scores, classes = np_outputs[0][0], np_outputs[1][0], np_outputs[2][0]
        if bbox_normalization:
            boxes = boxes / input_h

        if bbox_order == "xy":
            boxes = boxes[:, [1, 0, 3, 2]]
        boxes = np.array_split(boxes, 4, axis=1)
        boxes = zip(*boxes)

    # Don't filter by category - improve performance by accepting all detections
    last_detections = [
        Detection(box, category, score, metadata)
        for box, score, category in zip(boxes, scores, classes)
        if score > threshold
    ]
    return last_detections


def smooth_position(new_x, new_y):
    """Apply smoothing to position values using a moving average."""
    # Add new values to history
    position_history["x"].append(new_x)
    position_history["y"].append(new_y)
    
    # Calculate smoothed values (simple moving average)
    smoothed_x = sum(position_history["x"]) / len(position_history["x"])
    smoothed_y = sum(position_history["y"]) / len(position_history["y"])
    
    return smoothed_x, smoothed_y


def process_positions():
    """Process detections and calculate x, y position values with smoothing and persistence."""
    global position_data, last_detection_time
    
    # Get frame dimensions
    try:
        frame_width = picam2.camera_config["main"]["size"][0]
        frame_height = picam2.camera_config["main"]["size"][1]
    except:
        # Fallback default values
        frame_width = 640
        frame_height = 480
    
    frame_area = frame_width * frame_height
    current_time = time.time()
    
    # If we have detections, update position
    if last_detections:
        # Calculate average horizontal position and total area
        x_positions = []
        total_area = 0
        
        for detection in last_detections:
            x, y, w, h = detection.box
            
            # Calculate center of bounding box (horizontal)
            center_x = x + (w / 2)
            # Normalize to -1 to 1 range
            norm_x = (center_x / frame_width) * 2 - 1
            x_positions.append(norm_x)
            
            # Calculate detection area
            detection_area = w * h
            total_area += detection_area
        
        # Average horizontal position (-1 to 1)
        avg_x = sum(x_positions) / len(x_positions)
        
        # Calculate area ratio (total area / frame area)
        area_ratio = total_area / frame_area
        
        # Map area ratio to y value (-1 to 1)
        # -1 when area ratio > 50% (close), 1 when area ratio < 5% (far)
        if area_ratio > 0.5:
            y_value = -1
        elif area_ratio < 0.05:
            y_value = 1
        else:
            # Linear mapping between 5% and 50%
            y_value = 1 - (area_ratio - 0.05) * (2 / 0.45)
        
        # Apply smoothing
        smooth_x, smooth_y = smooth_position(avg_x, y_value)
        
        # Update position data
        position_data = {
            "x": smooth_x, 
            "y": smooth_y, 
            "num_people": len(last_detections)
        }
        
        # Update last detection time
        last_detection_time = current_time
    else:
        # No detections - check if we should hold the last position
        time_since_detection = current_time - last_detection_time
        
        if time_since_detection <= position_hold_time:
            # Keep the same position but mark that we're holding
            position_data["holding"] = True
        else:
            # After hold time expires, slowly return to center if no new detections
            if position_history["x"] and position_history["y"]:
                # Get last smoothed position
                last_x = position_data["x"]
                last_y = position_data["y"]
                
                # Move 10% closer to center each update
                decay_factor = 0.9
                new_x = last_x * decay_factor
                new_y = last_y * decay_factor
                
                # Apply smoothing
                smooth_x, smooth_y = smooth_position(new_x, new_y)
                
                position_data = {
                    "x": smooth_x,
                    "y": smooth_y,
                    "num_people": 0,
                    "holding": False
                }
            else:
                # If no history, just use zeros
                position_data = {"x": 0, "y": 0, "num_people": 0, "holding": False}
    
    return position_data


def draw_detections(request, stream="main"):
    """Draw the detections for this request onto the ISP output."""
    detections = last_results
    if detections is None:
        return
    with MappedArray(request, stream) as m:
        for detection in detections:
            x, y, w, h = detection.box
            
            # Draw detection box
            cv2.rectangle(m.array, (x, y), (x + w, y + h), (0, 255, 0, 0), thickness=2)

        # Display the position data on screen
        holding_text = " (holding)" if position_data.get("holding", False) else ""
        pos_text = f"Position X: {position_data['x']:.2f}, Y: {position_data['y']:.2f}, Objects: {position_data['num_people']}{holding_text}"
        cv2.putText(m.array, pos_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Draw visual indicator of current position
        center_x = int((position_data['x'] + 1) * m.array.shape[1] / 2)
        center_y = int((position_data['y'] + 1) * m.array.shape[0] / 2)
        cv2.circle(m.array, (center_x, center_y), 10, (255, 0, 0), -1)
        
        if intrinsics.preserve_aspect_ratio:
            b_x, b_y, b_w, b_h = imx500.get_roi_scaled(request)
            color = (255, 0, 0)  # red
            cv2.rectangle(m.array, (b_x, b_y), (b_x + b_w, b_y + b_h), (255, 0, 0, 0))


def start_http_server():
    """Start a simple HTTP server to serve position data to JavaScript clients."""
    import http.server
    import socketserver
    import json
    from urllib.parse import urlparse, parse_qs
    
    PORT = args.port
    
    class PositionHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            parsed_path = urlparse(self.path)
            
            # Serve position data as JSON for /position endpoint
            if parsed_path.path == '/position':
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')  # Allow CORS
                self.end_headers()
                
                # Create JSON response with position data
                response = json.dumps(position_data)
                self.wfile.write(response.encode('utf-8'))
                return
            
            # Serve a simple HTML page for testing
            elif parsed_path.path == '/' or parsed_path.path == '/index.html':
                self.send_response(200)
                self.send_header('Content-Type', 'text/html')
                self.end_headers()
                
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Position Tracker</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        #position-display { 
                            width: 400px; height: 400px; 
                            border: 2px solid black; 
                            position: relative;
                            background-color: #f0f0f0;
                        }
                        #position-indicator {
                            width: 20px; height: 20px;
                            background-color: red;
                            border-radius: 50%;
                            position: absolute;
                            transform: translate(-50%, -50%);
                            transition: all 0.3s ease;
                        }
                        #data { margin-top: 20px; }
                        .holding { background-color: orange; }
                    </style>
                </head>
                <body>
                    <h1>Position Tracker</h1>
                    <div id="position-display">
                        <div id="position-indicator"></div>
                    </div>
                    <div id="data">
                        <p>X: <span id="x-value">0.00</span></p>
                        <p>Y: <span id="y-value">0.00</span></p>
                        <p>Objects detected: <span id="object-count">0</span></p>
                        <p>Status: <span id="status">-</span></p>
                    </div>
                    
                    <script>
                        const indicator = document.getElementById('position-indicator');
                        const xValue = document.getElementById('x-value');
                        const yValue = document.getElementById('y-value');
                        const objectCount = document.getElementById('object-count');
                        const status = document.getElementById('status');
                        const display = document.getElementById('position-display');
                        
                        function updatePosition() {
                            fetch('/position')
                                .then(response => response.json())
                                .then(data => {
                                    // Update the indicator position
                                    const x = data.x;
                                    const y = data.y;
                                    
                                    // Convert from -1,1 to pixel coordinates
                                    const displayWidth = display.clientWidth;
                                    const displayHeight = display.clientHeight;
                                    
                                    const pixelX = ((x + 1) / 2) * displayWidth;
                                    const pixelY = ((y + 1) / 2) * displayHeight;
                                    
                                    indicator.style.left = pixelX + 'px';
                                    indicator.style.top = pixelY + 'px';
                                    
                                    // Update text values
                                    xValue.textContent = x.toFixed(2);
                                    yValue.textContent = y.toFixed(2);
                                    objectCount.textContent = data.num_people;
                                    
                                    // Update status and indicator style
                                    if (data.holding) {
                                        status.textContent = "Holding position";
                                        indicator.classList.add('holding');
                                    } else {
                                        status.textContent = data.num_people > 0 ? "Tracking" : "No detection";
                                        indicator.classList.remove('holding');
                                    }
                                })
                                .catch(error => console.error('Error fetching position data:', error));
                        }
                        
                        // Update position every 50ms
                        setInterval(updatePosition, 50);
                    </script>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
                return
                
            # Default 404 for other paths
            else:
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b'Not Found')
                return
    
    try:
        # Create server and start in a separate thread
        import threading
        server = socketserver.ThreadingTCPServer(("", PORT), PositionHandler)
        print(f"Starting HTTP server on port {PORT}")
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        print(f"Server running on http://localhost:{PORT}")
    except Exception as e:
        print(f"Error starting HTTP server: {e}")


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str, help="Path of the model",
                        default="/usr/share/imx500-models/imx500_network_ssd_mobilenetv2_fpnlite_320x320_pp.rpk")
    parser.add_argument("--fps", type=int, help="Frames per second")
    parser.add_argument("--bbox-normalization", action=argparse.BooleanOptionalAction, help="Normalize bbox")
    parser.add_argument("--bbox-order", choices=["yx", "xy"], default="yx",
                        help="Set bbox order yx -> (y0, x0, y1, x1) xy -> (x0, y0, x1, y1)")
    parser.add_argument("--threshold", type=float, default=0.55, help="Detection threshold")
    parser.add_argument("--iou", type=float, default=0.65, help="Set iou threshold")
    parser.add_argument("--max-detections", type=int, default=10, help="Set max detections")
    parser.add_argument("--ignore-dash-labels", action=argparse.BooleanOptionalAction, help="Remove '-' labels ")
    parser.add_argument("--postprocess", choices=["", "nanodet"],
                        default=None, help="Run post process of type")
    parser.add_argument("-r", "--preserve-aspect-ratio", action=argparse.BooleanOptionalAction,
                        help="preserve the pixel aspect ratio of the input tensor")
    parser.add_argument("--labels", type=str,
                        help="Path to the labels file")
    parser.add_argument("--print-intrinsics", action="store_true",
                        help="Print JSON network_intrinsics then exit")
    parser.add_argument("--port", type=int, default=8000,
                        help="Port for the HTTP server")
    parser.add_argument("--update-interval", type=float, default=0.05,
                        help="Interval in seconds to update position data")
    parser.add_argument("--hold-time", type=float, default=1.0,
                        help="Time in seconds to hold position after detection loss")
    parser.add_argument("--smoothing-window", type=int, default=5,
                        help="Number of frames to use for position smoothing")
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    # Apply smoothing window from args
    position_history = {
        "x": deque(maxlen=args.smoothing_window),
        "y": deque(maxlen=args.smoothing_window)
    }
    
    # Set position hold time from args
    position_hold_time = args.hold_time

    # This must be called before instantiation of Picamera2
    imx500 = IMX500(args.model)
    intrinsics = imx500.network_intrinsics
    if not intrinsics:
        intrinsics = NetworkIntrinsics()
        intrinsics.task = "object detection"
    elif intrinsics.task != "object detection":
        print("Network is not an object detection task", file=sys.stderr)
        exit()

    # Override intrinsics from args
    for key, value in vars(args).items():
        if key == 'labels' and value is not None:
            with open(value, 'r') as f:
                intrinsics.labels = f.read().splitlines()
        elif hasattr(intrinsics, key) and value is not None:
            setattr(intrinsics, key, value)

    # Defaults
    if intrinsics.labels is None:
        with open("assets/coco_labels.txt", "r") as f:
            intrinsics.labels = f.read().splitlines()
    intrinsics.update_with_defaults()

    if args.print_intrinsics:
        print(intrinsics)
        exit()

    picam2 = Picamera2(imx500.camera_num)
    config = picam2.create_preview_configuration(controls={"FrameRate": intrinsics.inference_rate}, buffer_count=12)

    imx500.show_network_fw_progress_bar()
    picam2.start(config, show_preview=True)

    if intrinsics.preserve_aspect_ratio:
        imx500.set_auto_aspect_ratio()

    # Start the HTTP server
    start_http_server()

    last_results = None
    picam2.pre_callback = draw_detections
    
    last_update_time = time.time()
    update_interval = args.update_interval  # seconds
    
    try:
        while True:
            # Capture and process detections
            last_results = parse_detections(picam2.capture_metadata())
            
            # Update position data at specified interval
            current_time = time.time()
            if current_time - last_update_time >= update_interval:
                process_positions()
                last_update_time = current_time
            
            # Small delay to prevent hogging CPU
            time.sleep(0.01)
    except KeyboardInterrupt:
        print("Stopping application...")
    finally:
        picam2.stop()
        print("Application stopped.")