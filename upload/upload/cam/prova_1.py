import argparse
import sys
import time
from functools import lru_cache

import cv2
import numpy as np

from picamera2 import MappedArray, Picamera2
from picamera2.devices import IMX500
from picamera2.devices.imx500 import (NetworkIntrinsics,
                                      postprocess_nanodet_detection)

# For storing the last detection results
last_detections = []
# For storing the processed position data
position_data = {"x": 0, "y": 0, "num_people": 0}


class Detection:
    def __init__(self, coords, category, conf, metadata):
        """Create a Detection object, recording the bounding box, category and confidence."""
        self.category = category
        self.conf = conf
        self.box = imx500.convert_inference_coords(coords, metadata, picam2)


def parse_detections(metadata: dict):
    """Parse the output tensor into a number of detected objects, scaled to the ISP output."""
    global last_detections
    bbox_normalization = intrinsics.bbox_normalization
    bbox_order = intrinsics.bbox_order
    threshold = args.threshold
    iou = args.iou
    max_detections = args.max_detections

    np_outputs = imx500.get_outputs(metadata, add_batch=True)
    input_w, input_h = imx500.get_input_size()
    if np_outputs is None:
        return last_detections
    if intrinsics.postprocess == "nanodet":
        boxes, scores, classes = \
            postprocess_nanodet_detection(outputs=np_outputs[0], conf=threshold, iou_thres=iou,
                                          max_out_dets=max_detections)[0]
        from picamera2.devices.imx500.postprocess import scale_boxes
        boxes = scale_boxes(boxes, 1, 1, input_h, input_w, False, False)
    else:
        boxes, scores, classes = np_outputs[0][0], np_outputs[1][0], np_outputs[2][0]
        if bbox_normalization:
            boxes = boxes / input_h

        if bbox_order == "xy":
            boxes = boxes[:, [1, 0, 3, 2]]
        boxes = np.array_split(boxes, 4, axis=1)
        boxes = zip(*boxes)

    last_detections = [
        Detection(box, category, score, metadata)
        for box, score, category in zip(boxes, scores, classes)
        if score > threshold
    ]
    return last_detections


@lru_cache
def get_labels():
    labels = intrinsics.labels

    if intrinsics.ignore_dash_labels:
        labels = [label for label in labels if label and label != "-"]
    return labels


def process_person_positions():
    """Process person detections and calculate x, y position values."""
    global position_data
    labels = get_labels()
    person_label_index = labels.index("person") if "person" in labels else None
    
    # If we don't have a person label, return default values
    if person_label_index is None:
        position_data = {"x": 0, "y": 0, "num_people": 0}
        return position_data
    
    # Filter detections to only include people
    people_detections = [d for d in last_detections if int(d.category) == person_label_index]
    num_people = len(people_detections)
    
    if num_people == 0:
        position_data = {"x": 0, "y": 0, "num_people": 0}
        return position_data
    
    # Get frame dimensions from camera config
    frame_width = picam2.camera_config["main"]["size"][0]
    frame_height = picam2.camera_config["main"]["size"][1]
    frame_area = frame_width * frame_height
    
    # Calculate average horizontal position and total area
    x_positions = []
    total_person_area = 0
    
    for detection in people_detections:
        x, y, w, h = detection.box
        
        # Calculate center of bounding box (horizontal)
        center_x = x + (w / 2)
        # Normalize to -1 to 1 range
        norm_x = (center_x / frame_width) * 2 - 1
        x_positions.append(norm_x)
        
        # Calculate detection area
        person_area = w * h
        total_person_area += person_area
    
    # Average horizontal position (-1 to 1)
    avg_x = sum(x_positions) / len(x_positions)
    
    # Calculate area ratio (total person area / frame area)
    area_ratio = total_person_area / frame_area
    
    # Map area ratio to y value (-1 to 1)
    # -1 when area ratio > 50% (close), 1 when area ratio < 5% (far)
    if area_ratio > 0.5:
        y_value = -1
    elif area_ratio < 0.05:
        y_value = 1
    else:
        # Linear mapping between 5% and 50%
        y_value = 1 - (area_ratio - 0.05) * (2 / 0.45)
    
    position_data = {"x": avg_x, "y": y_value, "num_people": num_people}
    return position_data


def draw_detections(request, stream="main"):
    """Draw the detections for this request onto the ISP output."""
    detections = last_results
    if detections is None:
        return
    labels = get_labels()
    with MappedArray(request, stream) as m:
        for detection in detections:
            x, y, w, h = detection.box
            label = f"{labels[int(detection.category)]} ({detection.conf:.2f})"

            # Calculate text size and position
            (text_width, text_height), baseline = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            text_x = x + 5
            text_y = y + 15

            # Create a copy of the array to draw the background with opacity
            overlay = m.array.copy()

            # Draw the background rectangle on the overlay
            cv2.rectangle(overlay,
                          (text_x, text_y - text_height),
                          (text_x + text_width, text_y + baseline),
                          (255, 255, 255),  # Background color (white)
                          cv2.FILLED)

            alpha = 0.30
            cv2.addWeighted(overlay, alpha, m.array, 1 - alpha, 0, m.array)

            # Draw text on top of the background
            cv2.putText(m.array, label, (text_x, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

            # Draw detection box
            cv2.rectangle(m.array, (x, y), (x + w, y + h), (0, 255, 0, 0), thickness=2)

        # Display the position data on screen
        pos_text = f"Position X: {position_data['x']:.2f}, Y: {position_data['y']:.2f}, People: {position_data['num_people']}"
        cv2.putText(m.array, pos_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        if intrinsics.preserve_aspect_ratio:
            b_x, b_y, b_w, b_h = imx500.get_roi_scaled(request)
            color = (255, 0, 0)  # red
            cv2.putText(m.array, "ROI", (b_x + 5, b_y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            cv2.rectangle(m.array, (b_x, b_y), (b_x + b_w, b_y + b_h), (255, 0, 0, 0))


def start_http_server():
    """Start a simple HTTP server to serve position data to JavaScript clients."""
    import http.server
    import socketserver
    import json
    from urllib.parse import urlparse, parse_qs
    
    PORT = args.port
    
    class PositionHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            parsed_path = urlparse(self.path)
            
            # Serve position data as JSON for /position endpoint
            if parsed_path.path == '/position':
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')  # Allow CORS
                self.end_headers()
                
                # Create JSON response with position data
                response = json.dumps(position_data)
                self.wfile.write(response.encode('utf-8'))
                return
            
            # Serve a simple HTML page for testing
            elif parsed_path.path == '/' or parsed_path.path == '/index.html':
                self.send_response(200)
                self.send_header('Content-Type', 'text/html')
                self.end_headers()
                
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Person Position Tracker</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        #position-display { 
                            width: 400px; height: 400px; 
                            border: 2px solid black; 
                            position: relative;
                            background-color: #f0f0f0;
                        }
                        #person-indicator {
                            width: 20px; height: 20px;
                            background-color: red;
                            border-radius: 50%;
                            position: absolute;
                            transform: translate(-50%, -50%);
                        }
                        #data { margin-top: 20px; }
                    </style>
                </head>
                <body>
                    <h1>Person Position Tracker</h1>
                    <div id="position-display">
                        <div id="person-indicator"></div>
                    </div>
                    <div id="data">
                        <p>X: <span id="x-value">0.00</span></p>
                        <p>Y: <span id="y-value">0.00</span></p>
                        <p>People detected: <span id="people-count">0</span></p>
                    </div>
                    
                    <script>
                        const indicator = document.getElementById('person-indicator');
                        const xValue = document.getElementById('x-value');
                        const yValue = document.getElementById('y-value');
                        const peopleCount = document.getElementById('people-count');
                        const display = document.getElementById('position-display');
                        
                        function updatePosition() {
                            fetch('/position')
                                .then(response => response.json())
                                .then(data => {
                                    // Update the indicator position
                                    const x = data.x;
                                    const y = data.y;
                                    
                                    // Convert from -1,1 to pixel coordinates
                                    const displayWidth = display.clientWidth;
                                    const displayHeight = display.clientHeight;
                                    
                                    const pixelX = ((x + 1) / 2) * displayWidth;
                                    const pixelY = ((y + 1) / 2) * displayHeight;
                                    
                                    indicator.style.left = pixelX + 'px';
                                    indicator.style.top = pixelY + 'px';
                                    
                                    // Update text values
                                    xValue.textContent = x.toFixed(2);
                                    yValue.textContent = y.toFixed(2);
                                    peopleCount.textContent = data.num_people;
                                })
                                .catch(error => console.error('Error fetching position data:', error));
                        }
                        
                        // Update position every 100ms
                        setInterval(updatePosition, 100);
                    </script>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
                return
                
            # Default 404 for other paths
            else:
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b'Not Found')
                return
    
    try:
        # Create server and start in a separate thread
        import threading
        server = socketserver.ThreadingTCPServer(("", PORT), PositionHandler)
        print(f"Starting HTTP server on port {PORT}")
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        print(f"Server running on http://localhost:{PORT}")
    except Exception as e:
        print(f"Error starting HTTP server: {e}")


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str, help="Path of the model",
                        default="/usr/share/imx500-models/imx500_network_ssd_mobilenetv2_fpnlite_320x320_pp.rpk")
    parser.add_argument("--fps", type=int, help="Frames per second")
    parser.add_argument("--bbox-normalization", action=argparse.BooleanOptionalAction, help="Normalize bbox")
    parser.add_argument("--bbox-order", choices=["yx", "xy"], default="yx",
                        help="Set bbox order yx -> (y0, x0, y1, x1) xy -> (x0, y0, x1, y1)")
    parser.add_argument("--threshold", type=float, default=0.55, help="Detection threshold")
    parser.add_argument("--iou", type=float, default=0.65, help="Set iou threshold")
    parser.add_argument("--max-detections", type=int, default=10, help="Set max detections")
    parser.add_argument("--ignore-dash-labels", action=argparse.BooleanOptionalAction, help="Remove '-' labels ")
    parser.add_argument("--postprocess", choices=["", "nanodet"],
                        default=None, help="Run post process of type")
    parser.add_argument("-r", "--preserve-aspect-ratio", action=argparse.BooleanOptionalAction,
                        help="preserve the pixel aspect ratio of the input tensor")
    parser.add_argument("--labels", type=str,
                        help="Path to the labels file")
    parser.add_argument("--print-intrinsics", action="store_true",
                        help="Print JSON network_intrinsics then exit")
    parser.add_argument("--port", type=int, default=8000,
                        help="Port for the HTTP server")
    parser.add_argument("--update-interval", type=float, default=0.1,
                        help="Interval in seconds to update position data")
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    # This must be called before instantiation of Picamera2
    imx500 = IMX500(args.model)
    intrinsics = imx500.network_intrinsics
    if not intrinsics:
        intrinsics = NetworkIntrinsics()
        intrinsics.task = "object detection"
    elif intrinsics.task != "object detection":
        print("Network is not an object detection task", file=sys.stderr)
        exit()

    # Override intrinsics from args
    for key, value in vars(args).items():
        if key == 'labels' and value is not None:
            with open(value, 'r') as f:
                intrinsics.labels = f.read().splitlines()
        elif hasattr(intrinsics, key) and value is not None:
            setattr(intrinsics, key, value)

    # Defaults
    if intrinsics.labels is None:
        with open("assets/coco_labels.txt", "r") as f:
            intrinsics.labels = f.read().splitlines()
    intrinsics.update_with_defaults()

    if args.print_intrinsics:
        print(intrinsics)
        exit()

    picam2 = Picamera2(imx500.camera_num)
    config = picam2.create_preview_configuration(controls={"FrameRate": intrinsics.inference_rate}, buffer_count=12)

    imx500.show_network_fw_progress_bar()
    picam2.start(config, show_preview=True)

    # Add fallback for frame dimensions in case camera_config doesn't have the expected structure
    try:
        print(f"Camera config: {picam2.camera_config}")
        # Make sure we have valid dimensions
        if "main" not in picam2.camera_config or "size" not in picam2.camera_config["main"]:
            # Try to get from camera configuration
            cam_info = picam2.camera_properties
            # Use default values if we can't get actual values
            frame_width = 640
            frame_height = 480
            print(f"Using default dimensions: {frame_width}x{frame_height}")
    except Exception as e:
        print(f"Warning: {e}")
        print("Using default frame dimensions")

    if intrinsics.preserve_aspect_ratio:
        imx500.set_auto_aspect_ratio()

    # Start the HTTP server
    start_http_server()

    last_results = None
    picam2.pre_callback = draw_detections
    
    last_update_time = time.time()
    update_interval = args.update_interval  # seconds
    
    try:
        while True:
            # Capture and process detections
            last_results = parse_detections(picam2.capture_metadata())
            
            # Update position data at specified interval
            current_time = time.time()
            if current_time - last_update_time >= update_interval:
                process_person_positions()
                last_update_time = current_time
            
            # Small delay to prevent hogging CPU
            time.sleep(0.01)
    except KeyboardInterrupt:
        print("Stopping application...")
    finally:
        picam2.stop()
        print("Application stopped.")