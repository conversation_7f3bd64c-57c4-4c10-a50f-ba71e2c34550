import argparse
import sys
import time
import threading
from functools import lru_cache

import cv2
import numpy as np

from picamera2 import MappedArray, Picamera2
from picamera2.devices import IMX500
from picamera2.devices.imx500 import (NetworkIntrinsics,
                                      postprocess_nanodet_detection)

# Global variables with more organized structure
class GlobalState:
    def __init__(self):
        self.last_detections = []
        self.position_data = {"x": 0, "y": 0, "num_people": 0, "last_update_time": 0}
        self.detection_timeout = 2.0  # seconds to keep position data valid

global_state = GlobalState()


class Detection:
    def __init__(self, coords, category, conf, metadata):
        """Create a Detection object, recording the bounding box, category and confidence."""
        self.category = category
        self.conf = conf
        self.box = imx500.convert_inference_coords(coords, metadata, picam2)


def parse_detections(metadata: dict):
    """Parse the output tensor into detected objects, scaled to the ISP output."""
    bbox_normalization = intrinsics.bbox_normalization
    bbox_order = intrinsics.bbox_order
    threshold = args.threshold
    iou = args.iou
    max_detections = args.max_detections

    np_outputs = imx500.get_outputs(metadata, add_batch=True)
    if np_outputs is None:
        return global_state.last_detections
        
    input_w, input_h = imx500.get_input_size()
    if intrinsics.postprocess == "nanodet":
        boxes, scores, classes = \
            postprocess_nanodet_detection(outputs=np_outputs[0], conf=threshold, iou_thres=iou,
                                          max_out_dets=max_detections)[0]
        from picamera2.devices.imx500.postprocess import scale_boxes
        boxes = scale_boxes(boxes, 1, 1, input_h, input_w, False, False)
    else:
        boxes, scores, classes = np_outputs[0][0], np_outputs[1][0], np_outputs[2][0]
        if bbox_normalization:
            boxes = boxes / input_h

        if bbox_order == "xy":
            boxes = boxes[:, [1, 0, 3, 2]]
        boxes = np.array_split(boxes, 4, axis=1)
        boxes = zip(*boxes)

    # Update detections only if we have valid results
    detections = [
        Detection(box, category, score, metadata)
        for box, score, category in zip(boxes, scores, classes)
        if score > threshold
    ]
    
    if detections:  # Only update if we detected something
        global_state.last_detections = detections
        
    return global_state.last_detections


@lru_cache(maxsize=8)
def get_labels():
    """Retrieve and cache the labels, filtering if needed."""
    labels = intrinsics.labels
    if intrinsics.ignore_dash_labels:
        labels = [label for label in labels if label and label != "-"]
    return labels


@lru_cache(maxsize=1)
def get_person_label_index():
    """Get and cache the index of 'person' in labels."""
    labels = get_labels()
    return labels.index("person") if "person" in labels else None


def process_person_positions():
    """Process person detections and calculate x, y position values with timeout."""
    current_time = time.time()
    person_label_index = get_person_label_index()
    
    # If we don't have a person label, return default values
    if person_label_index is None:
        return False
    
    # Filter detections to only include people
    people_detections = [d for d in global_state.last_detections if int(d.category) == person_label_index]
    num_people = len(people_detections)
    
    # If no people detected but we have recent data, keep using it
    if num_people == 0:
        # If our last position data is still valid based on timeout, don't update
        if current_time - global_state.position_data.get("last_update_time", 0) < global_state.detection_timeout:
            return False
        # Otherwise reset the position data
        global_state.position_data = {"x": 0, "y": 0, "num_people": 0, "last_update_time": current_time}
        return True
    
    # We have people detected, process their positions
    # Get frame dimensions from camera config
    try:
        frame_width = picam2.camera_config["main"]["size"][0]
        frame_height = picam2.camera_config["main"]["size"][1]
    except (KeyError, TypeError):
        # Fallback to default values
        frame_width = 640
        frame_height = 480
        
    frame_area = frame_width * frame_height
    
    # Calculate average horizontal position and total area
    x_positions = []
    total_person_area = 0
    
    for detection in people_detections:
        x, y, w, h = detection.box
        
        # Calculate center of bounding box (horizontal)
        center_x = x + (w / 2)
        # Normalize to -1 to 1 range
        norm_x = (center_x / frame_width) * 2 - 1
        x_positions.append(norm_x)
        
        # Calculate detection area
        person_area = w * h
        total_person_area += person_area
    
    # Average horizontal position (-1 to 1)
    avg_x = sum(x_positions) / len(x_positions)
    
    # Calculate area ratio (total person area / frame area)
    area_ratio = total_person_area / frame_area
    
    # Map area ratio to y value (-1 to 1)
    # -1 when area ratio > 50% (close), 1 when area ratio < 5% (far)
    if area_ratio > 0.5:
        y_value = -1
    elif area_ratio < 0.05:
        y_value = 1
    else:
        # Linear mapping between 5% and 50%
        y_value = 1 - (area_ratio - 0.05) * (2 / 0.45)
    
    global_state.position_data = {
        "x": avg_x, 
        "y": y_value, 
        "num_people": num_people,
        "last_update_time": current_time
    }
    return True


def draw_detections(request, stream="main"):
    """Draw the detections for this request onto the ISP output."""
    detections = global_state.last_detections
    if not detections:
        return
        
    labels = get_labels()
    with MappedArray(request, stream) as m:
        # Draw position data on screen first (always visible)
        pos_text = f"Position X: {global_state.position_data['x']:.2f}, Y: {global_state.position_data['y']:.2f}, People: {global_state.position_data['num_people']}"
        cv2.putText(m.array, pos_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Draw detection boxes
        for detection in detections:
            x, y, w, h = detection.box
            label = f"{labels[int(detection.category)]} ({detection.conf:.2f})"

            # Draw detection box
            cv2.rectangle(m.array, (x, y), (x + w, y + h), (0, 255, 0), thickness=2)

            # Calculate text size and position
            (text_width, text_height), baseline = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            text_x = x + 5
            text_y = y + 15

            # Create semi-transparent background for text
            overlay = np.zeros_like(m.array)
            cv2.rectangle(overlay,
                          (text_x - 2, text_y - text_height - 2),
                          (text_x + text_width + 2, text_y + baseline + 2),
                          (255, 255, 255),
                          cv2.FILLED)
            cv2.addWeighted(overlay, 0.3, m.array, 1.0, 0, m.array)

            # Draw text on top of the background
            cv2.putText(m.array, label, (text_x, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # Draw ROI if applicable
        if intrinsics.preserve_aspect_ratio:
            b_x, b_y, b_w, b_h = imx500.get_roi_scaled(request)
            cv2.putText(m.array, "ROI", (b_x + 5, b_y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            cv2.rectangle(m.array, (b_x, b_y), (b_x + b_w, b_y + b_h), (255, 0, 0), 1)


def start_http_server():
    """Start a simple HTTP server to serve position data to JavaScript clients."""
    import http.server
    import socketserver
    import json
    from urllib.parse import urlparse
    
    PORT = args.port
    
    class PositionHandler(http.server.SimpleHTTPRequestHandler):
        # Optimize server by disabling logging
        def log_message(self, format, *args):
            # Comment this line to re-enable logging
            pass
            
        def do_GET(self):
            parsed_path = urlparse(self.path)
            
            # Serve position data as JSON for /position endpoint
            if parsed_path.path == '/position':
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')  # Allow CORS
                self.end_headers()
                
                # Filter out internal fields before sending
                response_data = {k: v for k, v in global_state.position_data.items() 
                               if k not in ['last_update_time']}
                response = json.dumps(response_data)
                self.wfile.write(response.encode('utf-8'))
                return
            
            # Serve a simple HTML page for testing
            elif parsed_path.path == '/' or parsed_path.path == '/index.html':
                self.send_response(200)
                self.send_header('Content-Type', 'text/html')
                self.end_headers()
                
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Person Position Tracker</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        #position-display { 
                            width: 400px; height: 400px; 
                            border: 2px solid black; 
                            position: relative;
                            background-color: #f0f0f0;
                        }
                        #person-indicator {
                            width: 20px; height: 20px;
                            background-color: red;
                            border-radius: 50%;
                            position: absolute;
                            transform: translate(-50%, -50%);
                        }
                        #data { margin-top: 20px; }
                    </style>
                </head>
                <body>
                    <h1>Person Position Tracker</h1>
                    <div id="position-display">
                        <div id="person-indicator"></div>
                    </div>
                    <div id="data">
                        <p>X: <span id="x-value">0.00</span></p>
                        <p>Y: <span id="y-value">0.00</span></p>
                        <p>People detected: <span id="people-count">0</span></p>
                    </div>
                    
                    <script>
                        const indicator = document.getElementById('person-indicator');
                        const xValue = document.getElementById('x-value');
                        const yValue = document.getElementById('y-value');
                        const peopleCount = document.getElementById('people-count');
                        const display = document.getElementById('position-display');
                        
                        // Use a request timeout to avoid overwhelming the server
                        let lastRequestTime = 0;
                        const minRequestInterval = 100; // ms
                        
                        function updatePosition() {
                            const now = Date.now();
                            if (now - lastRequestTime < minRequestInterval) {
                                return;
                            }
                            
                            lastRequestTime = now;
                            fetch('/position')
                                .then(response => response.json())
                                .then(data => {
                                    // Update the indicator position
                                    const x = data.x;
                                    const y = data.y;
                                    
                                    // Convert from -1,1 to pixel coordinates
                                    const displayWidth = display.clientWidth;
                                    const displayHeight = display.clientHeight;
                                    
                                    const pixelX = ((x + 1) / 2) * displayWidth;
                                    const pixelY = ((y + 1) / 2) * displayHeight;
                                    
                                    indicator.style.left = pixelX + 'px';
                                    indicator.style.top = pixelY + 'px';
                                    
                                    // Update text values
                                    xValue.textContent = x.toFixed(2);
                                    yValue.textContent = y.toFixed(2);
                                    peopleCount.textContent = data.num_people;
                                })
                                .catch(error => console.error('Error fetching position data:', error));
                        }
                        
                        // Update position every 100ms
                        setInterval(updatePosition, 100);
                    </script>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
                return
                
            # Default 404 for other paths
            else:
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b'Not Found')
                return
    
    try:
        # Create server with thread pooling for better performance
        class ThreadedHTTPServer(socketserver.ThreadingMixIn, socketserver.TCPServer):
            daemon_threads = True
            allow_reuse_address = True
        
        server = ThreadedHTTPServer(("", PORT), PositionHandler)
        print(f"Starting HTTP server on port {PORT}")
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        print(f"Server running at http://localhost:{PORT}")
    except Exception as e:
        print(f"Error starting HTTP server: {e}")


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str, help="Path of the model",
                        default="/usr/share/imx500-models/imx500_network_ssd_mobilenetv2_fpnlite_320x320_pp.rpk")
    parser.add_argument("--fps", type=int, help="Frames per second")
    parser.add_argument("--bbox-normalization", action=argparse.BooleanOptionalAction, help="Normalize bbox")
    parser.add_argument("--bbox-order", choices=["yx", "xy"], default="yx",
                        help="Set bbox order yx -> (y0, x0, y1, x1) xy -> (x0, y0, x1, y1)")
    parser.add_argument("--threshold", type=float, default=0.55, help="Detection threshold")
    parser.add_argument("--iou", type=float, default=0.65, help="Set iou threshold")
    parser.add_argument("--max-detections", type=int, default=10, help="Set max detections")
    parser.add_argument("--ignore-dash-labels", action=argparse.BooleanOptionalAction, help="Remove '-' labels ")
    parser.add_argument("--postprocess", choices=["", "nanodet"],
                        default=None, help="Run post process of type")
    parser.add_argument("-r", "--preserve-aspect-ratio", action=argparse.BooleanOptionalAction,
                        help="preserve the pixel aspect ratio of the input tensor")
    parser.add_argument("--labels", type=str,
                        help="Path to the labels file")
    parser.add_argument("--print-intrinsics", action="store_true",
                        help="Print JSON network_intrinsics then exit")
    parser.add_argument("--port", type=int, default=8000,
                        help="Port for the HTTP server")
    parser.add_argument("--update-interval", type=float, default=0.1,
                        help="Interval in seconds to update position data")
    parser.add_argument("--detection-timeout", type=float, default=2.0,
                        help="Time in seconds to keep positions after no detections")
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    # This must be called before instantiation of Picamera2
    imx500 = IMX500(args.model)
    intrinsics = imx500.network_intrinsics
    if not intrinsics:
        intrinsics = NetworkIntrinsics()
        intrinsics.task = "object detection"
    elif intrinsics.task != "object detection":
        print("Network is not an object detection task", file=sys.stderr)
        exit()

    # Override intrinsics from args
    for key, value in vars(args).items():
        if key == 'labels' and value is not None:
            with open(value, 'r') as f:
                intrinsics.labels = f.read().splitlines()
        elif hasattr(intrinsics, key) and value is not None:
            setattr(intrinsics, key, value)

    # Defaults
    if intrinsics.labels is None:
        try:
            with open("assets/coco_labels.txt", "r") as f:
                intrinsics.labels = f.read().splitlines()
        except FileNotFoundError:
            print("Warning: Default label file not found. Using basic COCO labels.")
            intrinsics.labels = ["background", "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat"]
            
    intrinsics.update_with_defaults()

    if args.print_intrinsics:
        print(intrinsics)
        exit()

    # Set detection timeout from args
    global_state.detection_timeout = args.detection_timeout

    # Initialize camera
    picam2 = Picamera2(imx500.camera_num)
    config = picam2.create_preview_configuration(controls={"FrameRate": intrinsics.inference_rate}, buffer_count=12)

    imx500.show_network_fw_progress_bar()
    picam2.start(config, show_preview=True)

    # Start the HTTP server
    start_http_server()

    if intrinsics.preserve_aspect_ratio:
        imx500.set_auto_aspect_ratio()

    picam2.pre_callback = draw_detections
    
    last_update_time = time.time()
    update_interval = args.update_interval  # seconds
    
    # Use adaptive sleep to reduce CPU usage
    def adaptive_sleep(target_fps=20):
        """Sleep just enough to maintain the target FPS"""
        frame_time = 1.0 / target_fps
        start_time = time.time()
        def sleep_remaining():
            elapsed = time.time() - start_time
            remaining = max(0, frame_time - elapsed)
            if remaining > 0:
                time.sleep(remaining)
            return time.time() - start_time
        return sleep_remaining
    
    try:
        # Main loop with optimized processing
        sleep_manager = adaptive_sleep(30)  # Target 30 FPS for smooth operation
        
        while True:
            # Capture and process detections
            parse_detections(picam2.capture_metadata())
            
            # Update position data at specified interval
            current_time = time.time()
            if current_time - last_update_time >= update_interval:
                if process_person_positions():
                    last_update_time = current_time
            
            # Adaptive sleep to maintain target FPS
            frame_duration = sleep_manager()
            
    except KeyboardInterrupt:
        print("Stopping application...")
    except Exception as e:
        print(f"Error in main loop: {e}")
    finally:
        picam2.stop()
        print("Application stopped.")