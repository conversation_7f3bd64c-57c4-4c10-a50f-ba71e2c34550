import { Art } from './Art.js';
import { Tools } from './Tools.js';
import { Canvas } from './Canvas.js';
import { Point } from './Point.js';
import { Path } from './Path.js';
import { Polygon } from './Polygon.js';
import { Drawable } from './Drawable.js';
import { ContourLayer } from './ContourLayer.js';


export class Landscape{
    // dist=1200/16 //todo config relacionat amb canvas?
    generated=false
    layers = [];
   // dirs=[Art.RIGHT,Art.RIGHT-Art.TAU/8,Art.RIGHT+Art.TAU/8,Art.RIGHT-Art.TAU/16,Art.RIGHT+Art.TAU/16]  //param
    //dirs=[Art.RIGHT,Art.RIGHT-Art.TAU/8,Art.RIGHT+Art.TAU/8]  //param
    dirs=[Art.RIGHT,Art.RIGHT-Art.TAU/32,Art.RIGHT+Art.TAU/32]

    constructor(canvas) {

        this.canvas = canvas;
        this.width = canvas.width;
        this.height = canvas.height;
    }

    createLayers() {
        // Create 4 layers, each with different height positions, grid distances, and margins
        // Parameters: (height ratio, grid distance, margin ratio)
        // Height ratio: position on the canvas (0-1)
        // Grid distance: spacing between grid points for objects
        // Margin ratio: how much the layer extends beyond canvas boundaries

        // Layers are added in back-to-front order (further layers first)
       // this.createLayer(0.1, 120, 0.5)
       let step=Tools.randInt(5)*0.1
        this.createLayer(0.4-step, 100, 0.4)
        this.createLayer(0.5-step, 87.5, 0.3)
        this.createLayer(0.6-step, 75, 0.35)
       this.createLayer(0.7-step, 62.5, 0.2)
        this.createLayer(0.8-step, 50, 0.15)
        this.createLayer(0.9-step, 37.5, 0.1)


        // Generate grid points inside each layer's polygon
        this.generateSpots()

        // Remove points that would be hidden by layers in front
        this.removeInvisible()
    }

    //between 0 and 1
    createLayer(height,grid_dist,margin) {

        const slope=$fx.getParam("mountain_slope");
        this.dirs=[
            Art.RIGHT,
            Art.RIGHT-(Art.TAU/slope)*this.layers.length,
            Art.RIGHT+(Art.TAU/slope)*this.layers.length
        ]

      /*  if(this.layers.length==5){
            this.dirs=[
                Art.RIGHT,
                Art.RIGHT-(Art.TAU/8),
                Art.RIGHT+(Art.TAU/8)
            ]
        }*/
        let that = this;
        // Apply margin to bottom and top
        const bottomY = this.height + Art.height * margin;
        const layerY = bottomY - (height * this.height);

        // Starting point with margin on the left
        const p0 = new Point(0 - Art.width * margin, layerY);

        let currentDir = Tools.getRandFromArray(this.dirs);
        let dir = currentDir;
        let dist_travelled = 0;
        let inix = p0.x; // Use p0.x instead of hardcoded value
        let indx = 0;

        let poly = Path.getPolarLineCondition(
            p0,
            function(p) {
                return dir;
            },
            function(p) {
                dist_travelled += p.x - inix;

                // Change target direction every 400 units instead of 300
                if (dist_travelled > 200) { //param
                    currentDir = Tools.getRandFromArray(that.dirs);
                    dist_travelled = 0;
                }

                inix = p.x;

                // Calculate shortest angular difference
                let delta = currentDir - dir;
                delta = delta - 360 * Math.round(delta / 360);

                // Smooth interpolation with angular awareness
                dir += delta * 0.1; // param

                // Apply margin to the right boundary (consistent margin usage)
                return p.x < Art.width + Art.width * margin;
            }
        );
        // Define a simple fallback for getTangentAngle if needed
        if (!poly.getTangentAngle) {
            console.warn("Adding getTangentAngle method to poly");
            poly.getTangentAngle = function(indx) {
                let p1, p2;
                if (indx === 0) {
                    p1 = this.points[indx];
                    p2 = this.points[indx + 1];
                } else if (indx === this.points.length - 1) {
                    p1 = this.points[indx - 1];
                    p2 = this.points[indx];
                } else {
                    p1 = this.points[indx - 1];
                    p2 = this.points[indx + 1];
                }
                return Math.atan2(p2.y - p1.y, p2.x - p1.x);
            };
        }

        // Set angle and index for each point
        poly.points.forEach((p, indx) => {
            p.angle = poly.getTangentAngle(indx) + Art.TAU * 0.4;
            p.indx = indx;
        });

        // Close the polygon with points that form a complete closed shape
        // Right bottom corner with margin
        const rightX = Art.width + Art.width * margin;
        poly.addPoint(new Point(rightX, bottomY));

        // Left bottom corner with margin
        const leftX = 0 - Art.width * margin;
        poly.addPoint(new Point(leftX, bottomY));

        // Back to starting point
        poly.addPoint(p0);

        // Create a ContourLayer instead of a Polygon
        const layer = new ContourLayer(poly.points);
        layer.grid_dist = grid_dist*0.5; //param
        layer.margin = margin;

        // Set the contour spacing proportional to the grid distance
        // This ensures the contour lines scale with the perspective of each layer
        // For distant mountains (small grid_dist), use a larger percentage to avoid too many lines
        const minSpacingPx = 5 / Art.ratio; // Minimum spacing in logical units
        const proportionalSpacing = grid_dist * (grid_dist < 50 ? 0.3 : 0.2); // Adjust percentage based on distance
        layer.contourSpacing = Math.max(proportionalSpacing, minSpacingPx); // Use the larger value

        // Set a default style for the layer
        layer.setStyle({
            fillStyle: Art.palette.getRandColor("landscape"),
            strokeStyle: "#000000",
            lineWidth: grid_dist * 0.01 * Art.ratio
        });

        this.layers.unshift(layer);
    }



    generateSpots(){
        this.layers.forEach((layer, index) => {

            console.log("layer", layer.dist);
            layer.spots = this.generateGrid(layer,  layer.grid_dist);
            layer.spots.sort((a, b) => {
                if (a.y === b.y) {
                    return a.x - b.x;
                }
                return a.y - b.y;
            });
            layer.spots.forEach((spot, indx) => {
                spot.indx = indx;
            });
        });
        this.generated=true
    }

    generateGrid(layer, d) {
        let points = [];
        const margin = layer.margin || 0;

        // Start from negative margins and extend beyond canvas width/height based on margin
        const startX = 0 - Art.width * margin;
        const startY = 0 - Art.height * margin;
        const endX = Art.width + Art.width * margin;
        const endY = Art.height + Art.height * margin;

        // Generate grid points including margin areas (use layer's grid_dist for spacing)
        for (let x = startX; x <= endX; x += d) {
            for (let y = startY; y <= endY; y += d) {
                const point = new Point(x, y);

                // Check if the point is inside the layer's polygon shape
                if (layer.containsPoint(point)) {
                    points.push(point);
                }
            }
        }

        // Debug log
        console.log(`Generated ${points.length} grid points for layer with margin ${margin}`);

        return points;
    }

    registerDrawables(layer_index,callback){
        let layer = this.layers[layer_index];
        // Use the layer-specific grid distance for this layer
        const layerGridDist = layer.grid_dist || Art.grid_dist;

        layer.spots.forEach(point=>{
            //search how many next points there are
            let nextPoints = [];
            let c=1
            for(let i=point.indx+1;i<layer.spots.length;i++){
                if(layer.spots[i].x==point.x+layerGridDist*c && layer.spots[i].y==point.y){
                    nextPoints.push(layer.spots[i]);
                }else{
                    break;
                }
                c++
            }
            point.nextPoints = nextPoints;
            callback(point)
        })
    }



        //todo no pot estar aqui
    isPointVisible(point, index) {
        // Check if this point (in the current layer) is covered by any higher layer

        // For each higher layer (with larger index)
        for (let i = index+1; i < this.layers.length; i++) {
            let otherLayer = this.layers[i];
            // Check the actual point without any adjustment
            if (otherLayer.containsPoint(point)) {
                return false; // Point is covered by a higher layer
            }
        }
        return true; // Point is not covered by any higher layer
    }

    removeInvisible(){


        this.layers.forEach((layer, index) => {
            layer.spots.forEach(point => {
                let visible = this.isPointVisible(point, index);
                if(visible) {
                    point.visible = true;
                }else{
                    point.visible = false;
                }

          });
        });

    }


    debug() {
        if(!this.generated){
            throw("You must generate the spots first");
            return;
        }
        this.layers.forEach((layer, index) => {
            if(index==0){
            let col = 'rgba(255,255,255,0.5)';

            layer.style.fillStyle = col;

            //layer.fill();
            layer.stroke();

            console.log("index", index);
            //nomes debug
            layer.spots.forEach(point => {
                let visible = this.isPointVisible(point, index);
                if(visible) {
                    Art.ctx.fillStyle = 'red';
                    Art.ctx.fillRect
                    (point.x*Art.ratio+index,
                        point.y*Art.ratio,
                        5*Art.ratio,
                        5*Art.ratio);
                        point.visible = true
                } else {
                    Art.ctx.fillStyle = 'blue';
                    Art.ctx.fillRect(point.x*Art.ratio+index*14, point.y*Art.ratio, 8*Art.ratio, 8*Art.ratio);
                    point.visible = false;
                }
               // point.debug();
            });
        }

        });
    }
}

class gridPoint {
    constructor(x, y, n, m) {
        this.x = x;
        this.y = y;
    }
}