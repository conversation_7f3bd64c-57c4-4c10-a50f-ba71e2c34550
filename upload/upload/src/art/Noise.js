import { Art } from './Art.js';

// Self-contained Perlin noise implementation
export class Noise {
    constructor(seed = Art.rand() * 10000) {
        this.seed(seed);
        this.cx = Math.floor(Art.rand() * 10000);
        this.cy = Math.floor(Art.rand() * 10000);
    }
    
    seed(seed) {
        this.p = new Array(512);
        const permutation = new Array(256);
        
        // Initialize array with values 0-255
        for (let i = 0; i < 256; i++) {
            permutation[i] = i;
        }
        
        // Create seeded random function
        let n = 256;
        let randomFunc = () => {
            seed = (seed * 16807) % 2147483647;
            return seed / 2147483647;
        };
        
        // Shuffle permutation array using Fisher<PERSON><PERSON>
        while (n > 0) {
            const i = Math.floor(randomFunc() * n--);
            const t = permutation[n];
            permutation[n] = permutation[i];
            permutation[i] = t;
        }
        
        // Copy into expanded array
        for (let i = 0; i < 512; i++) {
            this.p[i] = permutation[i & 255];
        }
    }
    
    fade(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    lerp(t, a, b) {
        return a + t * (b - a);
    }
    
    grad(hash, x, y, z) {
        const h = hash & 15;
        const u = h < 8 ? x : y;
        const v = h < 4 ? y : (h === 12 || h === 14) ? x : z;
        return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
    }
    
    noise2D(x, y, resolution = 0.01) {
        x=x+resolution
        y=y+resolution
        
        x = (x + this.cx) * resolution;
        y = (y + this.cy) * resolution;
        
        const X = Math.floor(x) & 255;
        const Y = Math.floor(y) & 255;
        
        x -= Math.floor(x);
        y -= Math.floor(y);
        
        const u = this.fade(x);
        const v = this.fade(y);
        
        const A = this.p[X] + Y;
        const AA = this.p[A];
        const AB = this.p[A + 1];
        const B = this.p[X + 1] + Y;
        const BA = this.p[B];
        const BB = this.p[B + 1];
        
        return this.lerp(v, 
            this.lerp(u, 
                this.grad(this.p[AA], x, y, 0),
                this.grad(this.p[BA], x - 1, y, 0)
            ),
            this.lerp(u,
                this.grad(this.p[AB], x, y - 1, 0),
                this.grad(this.p[BB], x - 1, y - 1, 0)
            )
        );
    }
    
    // Fractal noise (multiple octaves)
    fractal2D(x, y, octaves = 4, lacunarity = 2.0, persistence = 0.5) {
        let total = 0;
        let frequency = 1;
        let amplitude = 1;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            total += this.noise2D(x * frequency, y * frequency) * amplitude;
            
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return total / maxValue;
    }
}