import { Art } from './Art.js';

export class Scene {
    constructor() {
        this.layers = [{ zIndex: 0, shapes: [] }];
        this.layerBitmaps = {};
        this.isLayerBitmapEnabled = false;
        this.count=0
    }

    addLayer(inlayer, zIndex) {
        // Ensure the inlayer has a margin property (default to 0 if not set)
        if (inlayer.margin === undefined) {
            inlayer.margin = 0;
        }

        // Add the layer to the scene
        this.add(inlayer, zIndex);

        // Find the layer we just added and set its margin
        let layer = this.layers.find(layer => layer.zIndex === zIndex);
        if (layer) {
            layer.margin = inlayer.margin;
            console.log(`Added layer with z-index ${zIndex} and margin ${layer.margin}`);
        }

        return this;
    }

    add(shape, zIndex = 0) {
        let layer = this.layers.find(layer => layer.zIndex === zIndex);
        if (!layer) {
            layer = { zIndex, shapes: [] };
            this.layers.push(layer);
            this.layers.sort((a, b) => a.zIndex - b.zIndex);
        }
        layer.shapes.push(shape);

        // Invalidate the layer bitmap if it exists
        if (this.layerBitmaps[zIndex]) {
            this.layerBitmaps[zIndex].isValid = false;
        }

        return this;
    }

    clear() {
        Art.clearRect(0, 0, Art.ctx.canvas.width, Art.ctx.canvas.height);
        return this;
    }

    /**
     * Creates or updates bitmaps for all layers or a specific layer
     * @param {number|null} zIndex - Optional zIndex to update only one layer
     * @returns {Scene} - Returns this for chaining
     */

    allPainted(){
        const maxShapes = Math.max(...this.layers.map(layer => layer.shapes.length));

        return this.count >= maxShapes;
    }
    createLayerBitmaps(zIndex = null) {
        // Enable layer bitmap mode
        this.isLayerBitmapEnabled = true;

        const layersToProcess = zIndex !== null
            ? this.layers.filter(layer => layer.zIndex === zIndex)
            : this.layers;

            this.count+=32 //velocitat

        for (const layer of layersToProcess) {


     
            // Create a canvas for this layer
            const canvas = document.createElement('canvas');

            // Ensure layer.margin is defined and valid
            const margin = layer.margin !== undefined ? layer.margin : 0;

            // Ensure we create a canvas with valid dimensions (at least 1x1)
            const canvasWidth = Math.max(1, Art.width * (1 + margin) * Art.ratio);
            const canvasHeight = Math.max(1, Art.height * (1 + margin) * Art.ratio);

            canvas.width = canvasWidth;
            canvas.height = canvasHeight;

       
            const ctx = canvas.getContext('2d', { alpha: true });


            // Clear the canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Save original context and swap
            const originalCtx = Art.ctx;
            Art.ctx = ctx;

            // Calculate offset to center content (account for the margin)
            // Use the margin variable we defined earlier
            const marginOffset = Art.width * margin * Art.ratio / 2;
            const marginOffsetY = Art.height * margin * Art.ratio / 2;

            // Save context state before translation
            Art.ctx.save();

            // Translate to account for margin (position shapes within the expanded canvas)
            // Center the content in the middle of the expanded canvas
            Art.ctx.translate(marginOffset, marginOffsetY);

            // Paint all shapes in this layer to the canvas
       
            let i=0
            for (const shape of layer.shapes) {
                // Check if the shape has a paint method before calling it
                if (typeof shape.paint === 'function') {
                    shape.paint();
                } else {
                    console.warn('Shape does not have a paint method:', shape);
                }
                i++
                if(i>this.count) break;
            }

            // Restore context state (remove translation)
            Art.ctx.restore();

            // Apply fog effect if enabled and this is a distant layer
            this.applyFogEffect(ctx, layer.zIndex, canvasWidth, canvasHeight);

            // Restore original context
            Art.ctx = originalCtx;

            // Create and store bitmap info
            this.layerBitmaps[layer.zIndex] = {
                canvas: canvas,
                ctx: ctx,
                zIndex: layer.zIndex,
                isValid: true
            };
        }

        return this;
    }

    /**
     * Apply fog effect to distant layers when fog parameter is enabled
     * @param {CanvasRenderingContext2D} ctx - The canvas context to apply fog to
     * @param {number} zIndex - The zIndex of the current layer
     * @param {number} canvasWidth - Width of the canvas
     * @param {number} canvasHeight - Height of the canvas
     */
    applyFogEffect(ctx, zIndex, canvasWidth, canvasHeight) {
        // Check if fog is enabled
        let fogEnabled = false;
        try {
            fogEnabled = typeof $fx !== 'undefined' ? $fx.getParam("fog") : false;
        } catch (e) {
            // If $fx is not available or parameter is not defined, fog is disabled
            fogEnabled = false;
        }

        if (!fogEnabled) {
            return; // Fog is disabled, do nothing
        }

        // Determine if this layer should have fog applied
        // Apply fog to distant layers, excluding the 2 closest ones
        // Based on the layer structure: sun layer (-1), landscape layers (0-5)
        // We want to exclude the 2 closest landscape layers (4 and 5)
        const shouldApplyFog = zIndex <= 3; // Apply to sun layer (-1) and landscape layers 0, 1, 2, 3

        if (!shouldApplyFog) {
            return; // This layer doesn't need fog
        }

        // Save the current context state
        ctx.save();

        // Set the fog properties - simple white overlay with 0.1 opacity
        ctx.globalCompositeOperation = 'source-over';
        ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';

        // Fill the entire canvas with the fog layer
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // Restore the context state
        ctx.restore();
    }

    /**
     * Paints a specific layer bitmap at the given position
     * @param {number} zIndex - The zIndex of the layer to paint
     * @param {number} x - X position
     * @param {number} y - Y position
     * @returns {Scene} - Returns this for chaining
     */
    paintLayerBitmap(zIndex, x = 0, y = 0) {
        // Handle case where layerBitmaps might not be initialized
        if (!this.layerBitmaps) {
            console.warn('Layer bitmaps not initialized');
            return this;
        }

        const bitmap = this.layerBitmaps[zIndex];
        const layer = this.layers.find(layer => layer.zIndex === zIndex);

        if (!bitmap || !bitmap.isValid) {
           // this.createLayerBitmaps(zIndex);
        }

        if (this.layerBitmaps[zIndex]) {
            const bitmap = this.layerBitmaps[zIndex];
            const canvas = bitmap.canvas;

            // Check if the canvas has valid dimensions before drawing
            if (canvas.width <= 0 || canvas.height <= 0) {
                console.warn(`Cannot draw layer ${zIndex}: Canvas has invalid dimensions (${canvas.width}x${canvas.height})`);
                return this;
            }

            // Calculate margin offset to position the layer correctly
            const margin = layer && layer.margin ? layer.margin : 0;

            // Position the layer bitmap so that the center of the actual content
            // aligns with the center of the canvas view
            const marginOffsetX = margin * Art.width * Art.ratio / 2;
            const marginOffsetY = margin * Art.height * Art.ratio / 2;

            // The x,y parameters represent the parallax offset from center
            try {
                Art.ctx.drawImage(
                    canvas,
                    x - marginOffsetX,
                    y - marginOffsetY
                );
            } catch (error) {
                console.error(`Error drawing layer ${zIndex}:`, error);
            }
        }

        return this;
    }

    /**
     * Paint all layer bitmaps in order
     * @returns {Scene} - Returns this for chaining
     */
    paintAllLayerBitmaps(x = 0, y = 0) {
        // Handle case where layerBitmaps might not be initialized
        if (!this.layerBitmaps) {
            console.warn('Layer bitmaps not initialized');
            return this;
        }

        // Sort layers by zIndex
        const layerIndices = Object.keys(this.layerBitmaps)
            .map(Number)
            .sort((a, b) => a - b); // This will sort negative indices first

        // Paint each layer bitmap
        for (const zIndex of layerIndices) {
            // Each layer will be positioned using its own margin offset in paintLayerBitmap
            this.paintLayerBitmap(zIndex, x, y);
        }

        return this;
    }

    /**
     * Enables or disables the use of layer bitmaps
     * @param {boolean} enabled - Whether to enable layer bitmaps
     * @returns {Scene} - Returns this for chaining
     */
    useLayerBitmaps(enabled = true) {
        this.isLayerBitmapEnabled = enabled;
        return this;
    }

    paint(zIndex = null) {
        if (this.isLayerBitmapEnabled) {
            if (zIndex !== null) {
                this.paintLayerBitmap(zIndex);
            } else {
                this.paintAllLayerBitmaps();
            }
            return this;
        }

        // Original painting logic when not using bitmaps
        if (zIndex !== null) {
            const layer = this.layers.find(layer => layer.zIndex === zIndex);
            if (layer) {
                layer.shapes.forEach(shape => {
                    if (typeof shape.paint === 'function') {
                        shape.paint(Art.ctx);
                    } else {
                        console.warn('Shape does not have a paint method:', shape);
                    }
                });
            }
        } else {
            this.layers.forEach(layer => {
                layer.shapes.forEach(shape => {
                    if (typeof shape.paint === 'function') {
                        shape.paint(Art.ctx);
                    } else {
                        console.warn('Shape does not have a paint method:', shape);
                    }
                });
            });
        }
        return this;
    }

    debug() {
        /*
        this.layers.forEach(layer => {
            layer.debug();
        });
        return this;*/
    }
}