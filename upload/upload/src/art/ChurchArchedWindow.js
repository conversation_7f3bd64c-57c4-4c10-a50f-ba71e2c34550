import { Art } from './Art.js';
import { Polygon } from './Polygon.js';
import { Point } from './Point.js';

export class ChurchArchedWindow extends Polygon {
    constructor(x, y, width, archHeight) {
        // Create points for the arch
        const points = [];
        
        // Number of segments to create the arch (more segments = smoother curve)
        const segments = 12;
        
        // Create the arch points using a half-circle approximation
        for (let i = 0; i <= segments; i++) {
            const angle = Math.PI - (i / segments) * Math.PI;
            const pointX = x + width/2 + (width/2) * Math.cos(angle);
            const pointY = y - archHeight * Math.sin(angle);
            points.push(new Point(pointX, pointY));
        }
        
        // Call the parent constructor with the points
        super(points);
        
        // Set default style
        this.setStyle({
            fillStyle: "#000033", // Dark blue for stained glass effect
            strokeStyle: "#000000",
            lineWidth: Art.grid_dist * 0.02
        });
    }
    
    paint() {
        // Save current context
        Art.ctx.save();
        
        // Fill the arch
        Art.ctx.beginPath();
        Art.moveTo(this.points[0].x, this.points[0].y);
        
        for (let i = 1; i < this.points.length; i++) {
            Art.lineTo(this.points[i].x, this.points[i].y);
        }
        
        // Close the path
        Art.ctx.closePath();
        
        // Apply styles
        Art.ctx.fillStyle = this.style.fillStyle;
        Art.ctx.strokeStyle = this.style.strokeStyle;
        Art.ctx.lineWidth = this.style.lineWidth;
        
        // Fill and stroke
        Art.ctx.fill();
        Art.ctx.stroke();
        
        // Restore context
        Art.ctx.restore();
    }
}
