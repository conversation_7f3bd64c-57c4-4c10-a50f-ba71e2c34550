// Class for handling multiple polygons
import { Drawable } from './Drawable.js'
import { Polygon } from './Polygon.js'
import { Point } from './Point.js'

export class MultiPolygon extends Drawable {

    constructor(polygons = []) {
        super();
        this.isFlipped = false; // Initialize isFlipped property
        this.polygons = polygons.map(({ points, zIndex }) => ({
            polygon: new Polygon(points),
            zIndex: zIndex || 0
        }));
    }

    addPolygon(polygon, zIndex = 0) {
        this.polygons.push({
            polygon,
            zIndex
        });
    }

    paint() {
        // Sort polygons by zIndex
        this.polygons.sort((a, b) => a.zIndex - b.zIndex);

        // Draw each polygon
        this.polygons.forEach(({ polygon }) => {
            polygon.paint();
        });
    }

    flipHorizontal() {
  
        // Find the bottom left point of the entire MultiPolygon
        const allPoints = this.polygons.flatMap(({ polygon }) => polygon.points);
        const leftX = Math.min(...allPoints.map(point => point.x));
        const bottomY = Math.max(...allPoints.map(point => point.y));
        
        // Calculate the rightmost point to determine width
        const rightX = Math.max(...allPoints.map(point => point.x));
        const width = rightX - leftX;
        
        // Calculate the center for flipping
        const centerX = (leftX + rightX) / 2;
        
        // Flip each polygon around the common center
        this.polygons.forEach(({ polygon }) => {
            polygon.points = polygon.points.map(point => 
                new Point(2 * centerX - point.x, point.y)
            );
        });
        
        // Find the new left bound after flipping
        const newAllPoints = this.polygons.flatMap(({ polygon }) => polygon.points);
        const newLeftX = Math.min(...newAllPoints.map(point => point.x));
        
        // Calculate the adjustment needed to maintain the original left position
        const adjustX = leftX - newLeftX;
        
        // Adjust all polygons to maintain the original left position
        this.polygons.forEach(({ polygon }) => {
            polygon.points = polygon.points.map(point => 
                new Point(point.x + adjustX, point.y)
            );
        });
        this.isFlipped = !this.isFlipped;
    }

    move(x, y) {
        this.polygons.forEach(({ polygon }) => {
            polygon.points = polygon.points.map(point => point.move(x, y));
        });
    }

    getBounds() {
        const allPoints = this.polygons.flatMap(({ polygon }) => polygon.points);
        const xCoords = allPoints.map(p => p.x);
        const yCoords = allPoints.map(p => p.y);
        
        return {
            minX: Math.min(...xCoords),
            maxX: Math.max(...xCoords),
            minY: Math.min(...yCoords),
            maxY: Math.max(...yCoords),
            width: Math.max(...xCoords) - Math.min(...xCoords),
            height: Math.max(...yCoords) - Math.min(...yCoords)
        };
    }
}