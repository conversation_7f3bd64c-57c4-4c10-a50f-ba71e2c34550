 import {Art} from './Art.js';

export class Tracking{
    mouseX = 0;
    mouseY=0;
    normalizedMouseX = 0;
    normalizedMouseY = 0;
    constructor(canvas,mode="mouse"){
        this.mode=mode;

        if(this.mode=="mouse"){
            // Track mouse position for parallax effect
            document.addEventListener('mousemove', (e) => {
                // Convert mouse position to canvas coordinates
                const rect = canvas.getBoundingClientRect();
                this.mouseX = ((e.clientX - rect.left) / rect.width) * Art.width;
                this.mouseY = ((e.clientY - rect.top) / rect.height) * Art.height;
                this.normalize();
            });
        }
        if(this.mode=="camera"){
            const serverIP = 'localhost'; // TODO: Make this configurable
            const serverPort = 8000;

            const updatePosition = () => {
                fetch(`http://${serverIP}:${serverPort}/position`)
                    .then(response => response.json())
                    .then(data => {
                        console.log(`Position: X=${data.x}, Y=${data.y}, People=${data.num_people}`);
                        this.normalizedMouseX += (data.x- this.normalizedMouseX)/4;
                        this.normalizedMouseY += (data.y- this.normalizedMouseY)/4;
                    })
                    .catch(error => console.error('Error fetching position data:', error));
            };

            // Update position every 100ms
            setInterval(updatePosition, 100);
        }
        if(this.mode=="test"){
            this.normalizedMouseX=0.5
            this.normalizedMouseY=0.5
            this.speedx=Art.rand()*0.1-0.05
            this.speedy=Art.rand()*0.1-0.05
            setInterval(() => {
                console.log("hehe", this.normalizedMouseX)
                this.normalizedMouseX+=this.speedx;
                this.normalizedMouseY+=this.speedy;
                if(this.normalizedMouseX>1 || this.normalizedMouseX<-1){
                    this.speedx*=-1
                }
                if(this.normalizedMouseY>1 || this.normalizedMouseY<-1){
                    this.speedy*=-1
                }
            }, 50);
            
         }
    }
    debug() {
        const crossSize = 0.1; // Normalized size
        const centerX = (this.normalizedMouseX + 1) / 2 * Art.width;
        const centerY = (this.normalizedMouseY + 1) / 2 * Art.height;
        Art.ctx.beginPath();
        Art.moveTo(centerX - crossSize * Art.width, centerY);
        Art.lineTo(centerX + crossSize * Art.width, centerY);
        Art.moveTo(centerX, centerY - crossSize * Art.height);
        Art.lineTo(centerX, centerY + crossSize * Art.height);
        Art.ctx.strokeStyle = 'red';
        Art.ctx.lineWidth = 2;
        Art.ctx.stroke();
       // console.log(this.normalizedMouseX, this.normalizedMouseY);
    }
    normalize(){
        this.normalizedMouseX = (this.mouseX / Art.width) * 2 - 1;
        this.normalizedMouseY = (this.mouseY / Art.height) * 2 - 1;
    }

    getTracking(){ 

        return [this.normalizedMouseX,this.normalizedMouseY];
    }

    settTracking(x,y){
        this.mouseX=x;
        this.mouseY=y;
    }
}