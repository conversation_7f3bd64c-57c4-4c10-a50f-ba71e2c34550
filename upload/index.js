/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ 466:
/***/ (function(module) {

// stats.js - http://github.com/mrdoob/stats.js
(function(f,e){ true?module.exports=e():0})(this,function(){var f=function(){function e(a){c.appendChild(a.dom);return a}function u(a){for(var d=0;d<c.children.length;d++)c.children[d].style.display=d===a?"block":"none";l=a}var l=0,c=document.createElement("div");c.style.cssText="position:fixed;top:0;left:0;cursor:pointer;opacity:0.9;z-index:10000";c.addEventListener("click",function(a){a.preventDefault();
u(++l%c.children.length)},!1);var k=(performance||Date).now(),g=k,a=0,r=e(new f.Panel("FPS","#0ff","#002")),h=e(new f.Panel("MS","#0f0","#020"));if(self.performance&&self.performance.memory)var t=e(new f.Panel("MB","#f08","#201"));u(0);return{REVISION:16,dom:c,addPanel:e,showPanel:u,begin:function(){k=(performance||Date).now()},end:function(){a++;var c=(performance||Date).now();h.update(c-k,200);if(c>g+1E3&&(r.update(1E3*a/(c-g),100),g=c,a=0,t)){var d=performance.memory;t.update(d.usedJSHeapSize/
1048576,d.jsHeapSizeLimit/1048576)}return c},update:function(){k=this.end()},domElement:c,setMode:u}};f.Panel=function(e,f,l){var c=Infinity,k=0,g=Math.round,a=g(window.devicePixelRatio||1),r=80*a,h=48*a,t=3*a,v=2*a,d=3*a,m=15*a,n=74*a,p=30*a,q=document.createElement("canvas");q.width=r;q.height=h;q.style.cssText="width:80px;height:48px";var b=q.getContext("2d");b.font="bold "+9*a+"px Helvetica,Arial,sans-serif";b.textBaseline="top";b.fillStyle=l;b.fillRect(0,0,r,h);b.fillStyle=f;b.fillText(e,t,v);
b.fillRect(d,m,n,p);b.fillStyle=l;b.globalAlpha=.9;b.fillRect(d,m,n,p);return{dom:q,update:function(h,w){c=Math.min(c,h);k=Math.max(k,h);b.fillStyle=l;b.globalAlpha=1;b.fillRect(0,0,r,m);b.fillStyle=f;b.fillText(g(h)+" "+e+" ("+g(c)+"-"+g(k)+")",t,v);b.drawImage(q,d+a,m,n-a,p,d,m,n-a,p);b.fillRect(d+n-a,m,a,p);b.fillStyle=l;b.globalAlpha=.9;b.fillRect(d+n-a,m,a,g((1-h/w)*p))}}};return f});


/***/ }),

/***/ 791:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   L: () => (/* binding */ Art)
/* harmony export */ });

class Art {
    // static const ratio=1
    static ratio=1
    static dist=10
    static grid_dist=50*0.25
    static TAU=Math.PI*2
    static ctx=null
    static palette=null

    static RIGHT=0
    static DOWN=Math.PI/2
    static LEFT=Math.PI
    static UP=3*Math.PI/2
    
    static style={
        lineWidth:4*Art.ratio,
        fillStyle:"white",
        strokeStyle:"black",

    }
    static old_style=Art.style


    static rand(){
        //return Art.rand()
        return $fx.rand()

    }

    static randInt(num){
        return Math.floor(Art.rand()*num)
    }
    
    static setCtx(ctx){
        Art.old_ctx=Art.ctx
        Art.ctx=ctx
    }

    static setLineWidth(width){
        Art.ctx.lineWidth=width*Art.ratio
    }   

    static restoreCtx(){
        Art.ctx=Art.old_ctx
    }


    static moveTo(x,y){
        Art.ctx.moveTo(x*Art.ratio,y*Art.ratio)
    }

    static lineTo(x,y){
        Art.ctx.lineTo(x*Art.ratio,y*Art.ratio)
    }

    static fillRect(x,y,width,height){
        Art.ctx.fillRect(x*Art.ratio,y*Art.ratio,width*Art.ratio,height*Art.ratio)
    }

    static clearRect(x,y,width,height){
        Art.ctx.clearRect(x*Art.ratio,y*Art.ratio,width*Art.ratio,height*Art.ratio)
    }

    static setPalette(palette){
        Art.palette=palette
    }

    /*
    static getColor(index){
        return Art.palette.getColor(index)
    }*/

    static applyStyle(){
        Art.ctx.lineWidth=Art.style.lineWidth
        Art.ctx.fillStyle=Art.style.fillStyle
        Art.ctx.strokeStyle=Art.style.strokeStyle
    }

    static setStyle(style){
        Art.old_style = { ...Art.style };
        Art.style = { ...Art.style, ...style };
        Art.applyStyle();
    }

    static restoreStyle(){
        Art.style=Art.old_style
        Art.applyStyle()
    }
 }
 

/***/ }),

/***/ 229:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Q: () => (/* binding */ Drawable)
/* harmony export */ });
/* harmony import */ var _Style_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(222);
/* harmony import */ var _Art_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(791);
// Base drawable class



class Drawable {
    isFlipped = false;

    constructor() {
      this.style = new _Style_js__WEBPACK_IMPORTED_MODULE_0__/* .Style */ .b();
      this.style.strokeStyle = _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.style.strokeStyle
      this.style.fillStyle = _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.style.fillStyle
      this.style.lineWidth = _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.style.lineWidth
      this.style.lineCap = _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.style.lineCap
      this.style.lineJoin = _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.style.lineJoin

      this.zIndex = 0;
    }

    setStyle(style) {
      if (!style) return this;

      // Only update properties that exist in the passed style object
      Object.keys(style).forEach(key => {
        if (this.style.hasOwnProperty(key)) {
          this.style[key] = style[key];
        }
      });

      this.style.apply();
      return this;
    }

    getBounds() {
      // Default implementation for general drawables
      return {
        minX: 0,
        minY: 0,
        maxX: _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.width || 800,
        maxY: _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.height || 600,
        width: _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.width || 800,
        height: _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.height || 600
      };
    }

    // Default paint method that does nothing
    // This ensures all Drawable objects have a paint method
    paint() {
      // Default implementation does nothing
      // Subclasses should override this method
    }

  }




/***/ }),

/***/ 959:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   y: () => (/* binding */ Path)
/* harmony export */ });
/* harmony import */ var _Drawable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(229);
/* harmony import */ var _Point_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(816);
/* harmony import */ var _Art_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(791);
// Class for handling paths





class Path extends _Drawable_js__WEBPACK_IMPORTED_MODULE_0__/* .Drawable */ .Q {
  isPolygon = false;

    constructor(points = []) {
      super();
      this.points = points.map(point => 
        point instanceof _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E ? point : new _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E(point.x, point.y)
      );
    }
  
    addPoint(point) {
      this.points.push(point instanceof _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E ? point : new _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E(point.x, point.y));
      return this;
    }
  
    clone(path) {
      this.points = path.points.map(point => new _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E(point.x, point.y));
      return this;
    }

    static getPolarLine(p, angle, distance, turn=0){
        let dist=0 
        let path=new Path()
        path.addPoint(p)
        let num=distance/_Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.dist
        let angleStep=turn/num

        while(dist<distance){
          p=p.movePolar(angle,_Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.dist)
          path.addPoint(p)
          dist+=_Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.dist
          angle+=angleStep
        }
        return path
    }

    getTangentAngle(indx) {
      let p1, p2;
      if (indx === 0) {
          p1 = this.points[indx];
          p2 = this.points[indx + 1];
      } else if (indx === this.points.length - 1) {
          p1 = this.points[indx - 1];
          p2 = this.points[indx];
      } else {
          p1 = this.points[indx - 1];
          p2 = this.points[indx + 1];
      }
      return Math.atan2(p2.y - p1.y, p2.x - p1.x);
  }

    
    //creates a path from a point and a condition and a turn function
    static getPolarLineCondition(p, fTurn, fCondition) {
      // Explicitly create a new Path instance
      let path = new Path()
      path.addPoint(p)
      let c = 0
      
      while(true) {
        // Calculate the next point
        let nextPoint = p.movePolar(fTurn(p), _Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.dist)
        
        // Check if the next point would exceed the condition
        if(!fCondition(nextPoint)) {
          break
        }
        
        // If not, update p to the next point and add it to the path
        p = nextPoint
        path.addPoint(p)
        
        c++
        if(c > 1000) {
          console.error("Infinite loop")
          break
        }
      }
      
      // Ensure the returned object is a proper Path instance
      console.log("Returning path:", path);
      console.log("Path is Path instance:", path instanceof Path);
      console.log("Path has getTangentAngle:", typeof path.getTangentAngle === 'function');
      
      return path
    }

  getLastPoint(){
    return this.points[this.points.length-1]
  }
  
    getLength() {
      let length = 0;
      for (let i = 1; i < this.points.length; i++) {
        length += this.points[i].distanceTo(this.points[i - 1]);
      }
      return length;
    }
  
    getPointAt(distance) {
      let currentDist = 0;
      
      for (let i = 1; i < this.points.length; i++) {
        const segmentLength = this.points[i].distanceTo(this.points[i - 1]);
        
        if (currentDist + segmentLength >= distance) {
          const t = (distance - currentDist) / segmentLength;
          const x = this.points[i - 1].x + (this.points[i].x - this.points[i - 1].x) * t;
          const y = this.points[i - 1].y + (this.points[i].y - this.points[i - 1].y) * t;
          return new _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E(x, y);
        }
        
        currentDist += segmentLength;
      }
      
      return this.points[this.points.length - 1].clone();
    }
  
    trace() {
      if (this.points.length < 2) return;
  
     // this.style.apply(ctx);
     
      _Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.ctx.beginPath();
      _Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.moveTo(this.points[0].x, this.points[0].y);
      
      for (let i = 1; i < this.points.length; i++) {
        _Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.lineTo(this.points[i].x, this.points[i].y);
      }
      // Only close the path if it's a polygon
      if (this.isPolygon) {
        _Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.lineTo(this.points[0].x, this.points[0].y);
      }

     // ctx.stroke();
 
    }
    
    stroke(){
     
      this.style.apply();
      this.trace()
      _Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.ctx.stroke();
    }


    move(x,y){
      this.points=this.points.map(p=>p.move(x,y))
    }



        /**
     * Creates a polygon by extruding a path with varying width
     * @param {Path} path - The path to extrude
     * @param {number} width - Base width of the extrusion in pixels
     * @param {Array} levels - Array of multipliers at different points along the path (0-1)
     * @returns {Polygon} A new polygon representing the extruded path
     */
    static extrude(path, width, levels = [1, 1]) {
      // Import Polygon if not already available in this scope
       const { Polygon } = __webpack_require__(729);
      
      if (path.points.length < 2) {
        console.error("Cannot extrude a path with fewer than 2 points");
        return new Polygon([]);
      }
      
      const leftSide = [];
      const rightSide = [];
      
      // Calculate the total path length for interpolation
      const totalLength = path.getLength();
      let accumulatedLength = 0;
      
      // Process each segment of the path
      for (let i = 0; i < path.points.length - 1; i++) {
        const p1 = path.points[i];
        const p2 = path.points[i + 1];
        
        // Calculate segment direction vector
        const dx = p2.x - p1.x;
        const dy = p2.y - p1.y;
        const segmentLength = Math.sqrt(dx * dx + dy * dy);
        
        // Calculate normalized perpendicular vector
        const nx = -dy / segmentLength;
        const ny = dx / segmentLength;
        
        // Calculate relative position along the path (0 to 1)
        const posStart = accumulatedLength / totalLength;
        accumulatedLength += segmentLength;
        const posEnd = accumulatedLength / totalLength;
        
        // Interpolate width multiplier at this position
        const startMultiplier = Path.interpolateMultiplier(posStart, levels);
        const endMultiplier = Path.interpolateMultiplier(posEnd, levels);
        
        // Create the extruded points for this segment
        const halfWidthStart = (width * startMultiplier) / 2;
        const halfWidthEnd = (width * endMultiplier) / 2;
        
        // First point of the segment
        if (i === 0) {
          leftSide.push(new _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E(
            p1.x + nx * halfWidthStart,
            p1.y + ny * halfWidthStart
          ));
          
          rightSide.push(new _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E(
            p1.x - nx * halfWidthStart,
            p1.y - ny * halfWidthStart
          ));
        }
        
        // Second point of the segment
        leftSide.push(new _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E(
          p2.x + nx * halfWidthEnd,
          p2.y + ny * halfWidthEnd
        ));
        
        rightSide.push(new _Point_js__WEBPACK_IMPORTED_MODULE_1__/* .Point */ .E(
          p2.x - nx * halfWidthEnd,
          p2.y - ny * halfWidthEnd
        ));
      }
      
      // Combine the left and right sides to form a closed polygon
      // The right side points need to be in reverse order
      const polygonPoints = [...leftSide, ...rightSide.reverse()];
      
      return new Polygon(polygonPoints);
    }
    
    /**
     * Helper method to interpolate width multiplier at a specific position
     * @param {number} position - Position along the path (0-1)
     * @param {Array} levels - Array of width multipliers
     * @returns {number} Interpolated width multiplier
     */
    static interpolateMultiplier(position, levels) {
      if (levels.length === 1) return levels[0];
      
      // Map position (0-1) to levels array
      const index = position * (levels.length - 1);
      const lowerIndex = Math.floor(index);
      const upperIndex = Math.ceil(index);
      
      // Handle edge cases
      if (lowerIndex === upperIndex) return levels[lowerIndex];
      
      // Linear interpolation between the two nearest levels
      const t = index - lowerIndex;
      return levels[lowerIndex] * (1 - t) + levels[upperIndex] * t;
    }
    debug(){
      //todo , ha de ser un canvi temporal d'estil
      this.style.strokeStyle="red"
      this.style.apply()
      this.trace()
      _Art_js__WEBPACK_IMPORTED_MODULE_2__/* .Art */ .L.ctx.stroke()
    }

  }



/***/ }),

/***/ 816:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   E: () => (/* binding */ Point)
/* harmony export */ });
/* harmony import */ var _Art_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(791);


// Base class for 2D points
class Point {
    constructor(x = 0, y = 0) {
      this.x = x;
      this.y = y;
    }
  
    add(point) {
      return new Point(this.x + point.x, this.y + point.y);
    }
    
    //d between 0 and 1
    getMiddle(point,d){
      return new Point(
        this.x+(point.x-this.x)*d,
        this.y+(point.y-this.y)*d
      )
    }

    move(x,y){
      return new Point(this.x+x,this.y+y)
    }
    
    movePolar(angle,dist){
      return new Point(
        this.x+dist*Math.cos(angle),
        this.y+dist*Math.sin(angle)
      )
    }

    distanceTo(point) {
      const dx = this.x - point.x;
      const dy = this.y - point.y;
      return Math.sqrt(dx * dx + dy * dy);
    }
  
    scale(factor) {
      return new Point(this.x * factor, this.y * factor);
    }
  
    clone() {
      return new Point(this.x, this.y);
    }

    debug(){
      _Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ctx.fillStyle = 'green';
      _Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ctx.fillRect(this.x*_Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ratio, this.y*_Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ratio, 5*_Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ratio, 5*_Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ratio);
    }
  }



/***/ }),

/***/ 729:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Polygon: () => (/* binding */ Polygon)
/* harmony export */ });
/* harmony import */ var _Drawable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(229);
/* harmony import */ var _Art_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(791);
/* harmony import */ var _Point_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(816);
/* harmony import */ var _Path_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(959);
// Class for handling paths






//it's a path closed
class Polygon extends _Path_js__WEBPACK_IMPORTED_MODULE_3__/* .Path */ .y {
    isPolygon = true;


    constructor(points = []) {
      super();
      this.points = points.map(point => 
        point instanceof _Point_js__WEBPACK_IMPORTED_MODULE_2__/* .Point */ .E ? point : new _Point_js__WEBPACK_IMPORTED_MODULE_2__/* .Point */ .E(point.x, point.y)
      );
      
    }

    getPoint(num){
        return this.points[num]
    }
    
    width(){
        let minx=100000
        let maxx=-100000
        this.points.forEach(p=>{
            minx=Math.min(minx,p.x)
            maxx=Math.max(maxx,p.x)
        })
        return maxx-minx
    }

    height(){
        let miny=100000
        let maxy=-100000
        this.points.forEach(p=>{
            miny=Math.min(miny,p.y)
            maxy=Math.max(maxy,p.y)
        })
        return maxy-miny
    }
    fill(){
        this.style.apply()
        this.trace()
        _Art_js__WEBPACK_IMPORTED_MODULE_1__/* .Art */ .L.ctx.fill()
    }
    paint(){
        this.stroke()
        this.fill()
    }


    getYAtX(x) {
        let points = this.points;
    
        for (let i = 0; i < points.length - 1; i++) {
            let p1 = points[i];
            let p2 = points[i + 1];
    
            // Comprovar si x està entre p1.x i p2.x
            if (p1.x <= x && x <= p2.x) {
                // Interpolació lineal per trobar y
                let t = (x - p1.x) / (p2.x - p1.x);
                return p1.y + t * (p2.y - p1.y);
            }
        }
    
        return null; // Fora dels límits de la capa
    }

 

    flipHorizontal() {
        // Get the left and bottom bounds before flipping
        let leftX = Math.min(...this.points.map(p => p.x));
        let bottomY = Math.max(...this.points.map(p => p.y));
        
        // Calculate width to determine the right bound
        let rightX = Math.max(...this.points.map(p => p.x));
        let width = rightX - leftX;
        
        // Flip points horizontally around their own center
        const centerX = (leftX + rightX) / 2;
        this.points = this.points.map(point => new _Point_js__WEBPACK_IMPORTED_MODULE_2__/* .Point */ .E(2 * centerX - point.x, point.y));
        
        // Get the new left bound after flipping
        let newLeftX = Math.min(...this.points.map(p => p.x));
        
        // Calculate the adjustment needed to maintain the original left position
        let adjustX = leftX - newLeftX;
        
        // Adjust all points to maintain the original left bottom position
        this.points = this.points.map(point => new _Point_js__WEBPACK_IMPORTED_MODULE_2__/* .Point */ .E(point.x + adjustX, point.y));
    }
    
    containsPoint(point) {
        let inside = false;
        let points = this.points;
        let j = points.length - 1;
    
        for (let i = 0; i < points.length; i++) {
            let xi = points[i].x, yi = points[i].y;
            let xj = points[j].x, yj = points[j].y;
    
            let intersect = ((yi > point.y) !== (yj > point.y)) &&
                            (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);
            if (intersect) inside = !inside;
            j = i;
        }
        return inside;
    }
    
    getBounds() {
        const xCoords = this.points.map(p => p.x);
        const yCoords = this.points.map(p => p.y);
        return {
            minX: Math.min(...xCoords),
            maxX: Math.max(...xCoords),
            minY: Math.min(...yCoords),
            maxY: Math.max(...yCoords),
            width: Math.max(...xCoords) - Math.min(...xCoords),
            height: Math.max(...yCoords) - Math.min(...yCoords)
        };
    }

    /*

    clone() {
        const newPolygon = new Polygon();
        // Clone all points
        newPolygon.points = this.points.map(point => new Point(point.x, point.y));
        
        // Clone style if it exists
        if (this.style) {
            newPolygon.style = Object.assign({}, this.style);
        }else{
            newPolygon.style=new Style()
        }
        
        return newPolygon;
    }*/

}

// This class has been moved to MultiPolygon.js

/***/ }),

/***/ 222:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   b: () => (/* binding */ Style)
/* harmony export */ });
/* harmony import */ var _Art_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(791);


class Style {
    constructor({
      strokeStyle = '#404757',
      fillStyle = '#385E6F',
      lineWidth = 4*_Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ratio,
      lineCap = 'butt',
      lineJoin = 'miter'
    } = {}) {
      this.strokeStyle = strokeStyle;
      this.fillStyle = fillStyle;
      this.lineWidth = lineWidth;
      this.lineCap = lineCap;
      this.lineJoin = lineJoin;
    }
    
    setLineWidth(width) {
      this.lineWidth = width*_Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ratio;
      return this;
    }

    apply(customCtx) {
      // Use provided context or fall back to Art.ctx
      const ctx = customCtx || _Art_js__WEBPACK_IMPORTED_MODULE_0__/* .Art */ .L.ctx;
      
      if (!ctx) {
        console.error("No context available for Style.apply()");
        return;
      }
      
      const { strokeStyle, fillStyle, lineWidth, lineCap, lineJoin } = this;
      Object.assign(ctx, { strokeStyle, fillStyle, lineWidth, lineCap, lineJoin });
    }

      clone() {
      return new Style({
        strokeStyle: this.strokeStyle,
        fillStyle: this.fillStyle,
        lineWidth: this.lineWidth,
        lineCap: this.lineCap,
        lineJoin: this.lineJoin
      });
    }

  }

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry need to be wrapped in an IIFE because it need to be in strict mode.
(() => {
"use strict";

// EXTERNAL MODULE: ./src/art/Art.js
var art_Art = __webpack_require__(791);
;// CONCATENATED MODULE: ./src/art/Tools.js


class Tools {
    
    static getRandFromArray(arr){
        return arr[Math.floor(art_Art/* Art */.L.rand() * arr.length)];    
    }

    static randInt(num){
        return Math.floor(art_Art/* Art */.L.rand()*num)
    }


    //pattern like {1:0.5,2:0.5,3:0.5}
    static randPattern(pattern,num) {
        // Remove all values from the pattern that are greater than num
        for (const key in pattern) {
            if (key > num) {
                delete pattern[key];
            }
        }

        // Get total probability sum (in case they don't add up to 1)
        const total = Object.values(pattern).reduce((sum, probability) => sum + probability, 0);
        
        // Generate random value between 0 and total
        const rand = art_Art/* Art */.L.rand() * total;
        
        // Iterate through keys and find which range our random value falls into
        let cumulativeProbability = 0;
        for (const key in pattern) {
            cumulativeProbability += pattern[key];
            if (rand <= cumulativeProbability) {
                return key;
            }
        }
        
        // Fallback - should rarely get here unless there's a floating point precision issue
        return Object.keys(pattern)[Object.keys(pattern).length - 1];
    }

    // Adjust a color's brightness (positive values make it lighter, negative make it darker)
    static adjustColorBrightness(hexColor, factor) {
        // Parse hex color to RGB
        const r = parseInt(hexColor.slice(1, 3), 16);
        const g = parseInt(hexColor.slice(3, 5), 16);
        const b = parseInt(hexColor.slice(5, 7), 16);
        
        // Adjust the brightness
        let newR, newG, newB;
        if (factor < 0) {
            // Darken
            newR = Math.max(0, Math.round(r * (1 + factor)));
            newG = Math.max(0, Math.round(g * (1 + factor)));
            newB = Math.max(0, Math.round(b * (1 + factor)));
        } else {
            // Lighten
            newR = Math.min(255, Math.round(r + (255 - r) * factor));
            newG = Math.min(255, Math.round(g + (255 - g) * factor));
            newB = Math.min(255, Math.round(b + (255 - b) * factor));
        }
        
        // Convert back to hex
        return '#' + 
            newR.toString(16).padStart(2, '0') + 
            newG.toString(16).padStart(2, '0') + 
            newB.toString(16).padStart(2, '0');
    }
}

// EXTERNAL MODULE: ./src/art/Point.js
var art_Point = __webpack_require__(816);
;// CONCATENATED MODULE: ./src/art/Canvas.js



class Canvas {
    constructor(canvas) {
        this.canvas = canvas;
        art_Art/* Art */.L.ctx = canvas.getContext('2d');
        art_Art/* Art */.L.ctx.lineCap = 'round';
        art_Art/* Art */.L.ctx.lineJoin = 'round';

        this.width =art_Art/* Art */.L.width;
        this.height = art_Art/* Art */.L.height;
        this.canvas.width = art_Art/* Art */.L.width*art_Art/* Art */.L.ratio;
        this.canvas.height = art_Art/* Art */.L.height*art_Art/* Art */.L.ratio;

        this.lt=new art_Point/* Point */.E(0,0)
        this.rt=new art_Point/* Point */.E(art_Art/* Art */.L.width,0)
        this.lb=new art_Point/* Point */.E(0,art_Art/* Art */.L.height)
        this.rb=new art_Point/* Point */.E(art_Art/* Art */.L.width,art_Art/* Art */.L.height)

    }

    setRatio(ratio){
        art_Art/* Art */.L.ratio = ratio;
        this.canvas.width = art_Art/* Art */.L.width*art_Art/* Art */.L.ratio;
        this.canvas.height = art_Art/* Art */.L.height*art_Art/* Art */.L.ratio;
      
    }

 }
 
// EXTERNAL MODULE: ./src/art/Path.js
var Path = __webpack_require__(959);
// EXTERNAL MODULE: ./src/art/Polygon.js
var art_Polygon = __webpack_require__(729);
// EXTERNAL MODULE: ./src/art/Drawable.js
var art_Drawable = __webpack_require__(229);
;// CONCATENATED MODULE: ./src/art/MultiPolygon.js
// Class for handling multiple polygons




class MultiPolygon extends art_Drawable/* Drawable */.Q {

    constructor(polygons = []) {
        super();
        this.isFlipped = false; // Initialize isFlipped property
        this.polygons = polygons.map(({ points, zIndex }) => ({
            polygon: new art_Polygon.Polygon(points),
            zIndex: zIndex || 0
        }));
    }

    addPolygon(polygon, zIndex = 0) {
        this.polygons.push({
            polygon,
            zIndex
        });
    }

    paint() {
        // Sort polygons by zIndex
        this.polygons.sort((a, b) => a.zIndex - b.zIndex);

        // Draw each polygon
        this.polygons.forEach(({ polygon }) => {
            polygon.paint();
        });
    }

    flipHorizontal() {
  
        // Find the bottom left point of the entire MultiPolygon
        const allPoints = this.polygons.flatMap(({ polygon }) => polygon.points);
        const leftX = Math.min(...allPoints.map(point => point.x));
        const bottomY = Math.max(...allPoints.map(point => point.y));
        
        // Calculate the rightmost point to determine width
        const rightX = Math.max(...allPoints.map(point => point.x));
        const width = rightX - leftX;
        
        // Calculate the center for flipping
        const centerX = (leftX + rightX) / 2;
        
        // Flip each polygon around the common center
        this.polygons.forEach(({ polygon }) => {
            polygon.points = polygon.points.map(point => 
                new art_Point/* Point */.E(2 * centerX - point.x, point.y)
            );
        });
        
        // Find the new left bound after flipping
        const newAllPoints = this.polygons.flatMap(({ polygon }) => polygon.points);
        const newLeftX = Math.min(...newAllPoints.map(point => point.x));
        
        // Calculate the adjustment needed to maintain the original left position
        const adjustX = leftX - newLeftX;
        
        // Adjust all polygons to maintain the original left position
        this.polygons.forEach(({ polygon }) => {
            polygon.points = polygon.points.map(point => 
                new art_Point/* Point */.E(point.x + adjustX, point.y)
            );
        });
        this.isFlipped = !this.isFlipped;
    }

    move(x, y) {
        this.polygons.forEach(({ polygon }) => {
            polygon.points = polygon.points.map(point => point.move(x, y));
        });
    }

    getBounds() {
        const allPoints = this.polygons.flatMap(({ polygon }) => polygon.points);
        const xCoords = allPoints.map(p => p.x);
        const yCoords = allPoints.map(p => p.y);
        
        return {
            minX: Math.min(...xCoords),
            maxX: Math.max(...xCoords),
            minY: Math.min(...yCoords),
            maxY: Math.max(...yCoords),
            width: Math.max(...xCoords) - Math.min(...xCoords),
            height: Math.max(...yCoords) - Math.min(...yCoords)
        };
    }
}
;// CONCATENATED MODULE: ./src/art/PolygonForms.js




class Rectangle extends (/* unused pure expression or super */ null && (Polygon)) {
    constructor(x, y, width, height) {
        const points = [];
        const step = Art.dist;

        // Top edge
        for (let i = 0; i <= width; i += step) {
            points.push(new Point(x + i, y));
        }
        // Right edge
        for (let i = 0; i <= height; i += step) {
            points.push(new Point(x + width, y + i));
        }
        // Bottom edge
        for (let i = width; i >= 0; i -= step) {
            points.push(new Point(x + i, y + height));
        }
        // Left edge
        for (let i = height; i >= 0; i -= step) {
            points.push(new Point(x, y + i));
        }

        super(points);
    }
}
class Ellipse extends art_Polygon.Polygon {
    constructor(cx, cy, rx, ry,step=art_Art/* Art */.L.dist) {
        const points = [];
       // const step = Art.dist;
        const circumference = 2 * Math.PI * Math.max(rx, ry);
        const numPoints = Math.ceil(circumference / step);

        for (let i = 0; i < numPoints; i++) {
            const angle = (i / numPoints) * 2 * Math.PI;
            points.push(new art_Point/* Point */.E(
                cx + rx * Math.cos(angle),
                cy + ry * Math.sin(angle)
            ));
        }

        super(points);
    }
}

class Triangle extends (/* unused pure expression or super */ null && (Polygon)) {
    constructor(p1, p2, p3) {
        const points = [];
        const step = Art.dist;

        // Edge from p1 to p2
        const dist1 = Math.hypot(p2.x - p1.x, p2.y - p1.y);
        const steps1 = Math.ceil(dist1 / step);
        for (let i = 0; i <= steps1; i++) {
            const t = i / steps1;
            points.push(new Point(p1.x + t * (p2.x - p1.x), p1.y + t * (p2.y - p1.y)));
        }

        // Edge from p2 to p3
        const dist2 = Math.hypot(p3.x - p2.x, p3.y - p2.y);
        const steps2 = Math.ceil(dist2 / step);
        for (let i = 0; i <= steps2; i++) {
            const t = i / steps2;
            points.push(new Point(p2.x + t * (p3.x - p2.x), p2.y + t * (p3.y - p2.y)));
        }

        // Edge from p3 to p1
        const dist3 = Math.hypot(p1.x - p3.x, p1.y - p3.y);
        const steps3 = Math.ceil(dist3 / step);
        for (let i = 0; i <= steps3; i++) {
            const t = i / steps3;
            points.push(new Point(p3.x + t * (p1.x - p3.x), p3.y + t * (p1.y - p3.y)));
        }

        super(points);
    }
}
;// CONCATENATED MODULE: ./src/art/House.js







class House extends MultiPolygon {

    sunIntensity = 0.1; //param 0.05 -0.15
    static roof_angle=1.5 //param 1.5 2 2.5 3 3.5
    constructor(point, gridd_w, gridd_h, type="house") {
        super();

        this.type = type;
        // Store the current grid_dist value for later use in paint method
        this.currentGridDist = art_Art/* Art */.L.grid_dist;
        this.width = gridd_w*art_Art/* Art */.L.grid_dist;
        this.height = gridd_h*art_Art/* Art */.L.grid_dist;

        /* no fa res
        this.setStyle({
            lineWidth: 14
        });*/

        // Save the bottom left point
        this.point = point;


        //param fer dos tipus de casa, amb el costat mes ample o no
        let factorh = 0.; //param


        if(gridd_w==5 && gridd_h==3){
            factorh=3/(5*House.roof_angle)
        }
        factorh=gridd_h/(gridd_w*House.roof_angle)
        /*
        if(gridd_w==3 && gridd_h==2){
            factorh=0.25
        }
        if(gridd_w==6 && gridd_h==4){
            factorh=0.25
        }*/
        let factorv = 0.5; //param

        // Bottom points
        let b0 = point; // bottom left is now our starting point


        let b2 = point.move(this.width, 0); // bottom right

        // Instead of moving upward with negative height, we need positive values
        // as the canvas y-coordinate increases downward
        let t0 = b0.move(0, -this.height); // top left (moving up means decreasing y)
        let t2 = b2.move(0, -this.height); // top right

        // Middle points
        let t1 = t0.getMiddle(t2, factorh); // middle top
        let tp = t0.getMiddle(t1, 0.5); // peak point

        // Middle height points
        let m0 = b0.getMiddle(t0, factorv); // middle left
        let m2 = b2.getMiddle(t2, factorv); // middle right
        let m1 = m0.getMiddle(m2, factorh); // middle center
        let b1 = b0.move(this.width * factorh, 0); // middle bottom

        let d = t0.distanceTo(tp);
        let tr = t2.move(-d, 0); // top right roof point

        // Define the walls using the new coordinates
        // Create walls
        this.frontalWall = new art_Polygon.Polygon([
            m0, tp, m1, b1, b0
        ]);


        this.lateralWall = new art_Polygon.Polygon([
            m1, m2, b2, b1
        ]);




       // const col=Art.palette.getRealRandomColor(0.3)
       // this.lateralWall.style.fillStyle=col
        //this.frontalWall.style.fillStyle =col

        // Cache the roof points for later use
        this.roofPoints = [m1, tp, tr, m2];

        // Randomly decide whether to add a door or windows to the frontal wall (50% chance)
        this.hasDoor = art_Art/* Art */.L.rand() < 0.5;

        // Create the roof during initial construction, but don't style it yet
        const roof = new art_Polygon.Polygon(this.roofPoints);
        this.addPolygon(roof, 2); // Roof with highest z-index
        this.addPolygon(this.frontalWall, 1); // Frontal wall with middle z-index
        this.addPolygon(this.lateralWall, 0); // Lateral wall with lowest z-index

        if (this.hasDoor) {
            // Add a door to the frontal wall
            let door = this.createDoor();
    
            this.addPolygon(door, 1); // Door on the frontal wall
        }

        // Create windows for both walls
        let windows = this.createWindows();
        windows.forEach(w => this.addPolygon(w, 1));

        this.createMoreStuff()
    }

    createMoreStuff(){
        //todo
    }

    createWindows() {
        // Create windows for the lateral wall
        const lateralWindows = this.createWindowsForWall(this.lateralWall);

        // If the house doesn't have a door, create windows for the frontal wall
        if (!this.hasDoor) {
            // Create frontal windows with the special pattern (1 on top, 2 on bottom)
            const frontalWindows = this.createFrontalWindows();
            return [...lateralWindows, ...frontalWindows];
        }

        // If the house has a door, only return lateral windows
        return lateralWindows;
    }

    /**
     * Creates windows for the frontal wall
     * - For small houses: 1 window in the center
     * - For larger houses: 3 windows (1 on top, 2 on bottom)
     * @returns {Array} Array of window polygons
     */
    createFrontalWindows() {
        const windows = [];

        // Get dimensions of the frontal wall
        const wallWidth = this.frontalWall.width();
        const wallHeight = this.frontalWall.height();

        // Find the bounding box of the wall
        const xValues = this.frontalWall.points.map(p => p.x);
        const yValues = this.frontalWall.points.map(p => p.y);
        const minX = Math.min(...xValues);
        const minY = Math.min(...yValues);

        // Add wall margin - ensure windows don't touch the edges
        const wallMargin = art_Art/* Art */.L.grid_dist * 0.15;

        // Window parameters
        const windowSize = art_Art/* Art */.L.grid_dist * 0.35; // Slightly smaller than door

        // Check if this is a small house (grid width of 2 or less)
        // We can access the original grid width (gridd_w) that was used to construct the house
        // This is more reliable than checking the calculated wall dimensions
        const isSmallHouse = this.width / art_Art/* Art */.L.grid_dist <= 2;

        if (isSmallHouse) {
            // For small houses, create just one window at the bottom
            const bottomWindowX = minX + (wallWidth - windowSize) / 2;
            // Position the window in the lower part of the wall (similar to the bottom windows in larger houses)
            const bottomWindowY = minY + wallHeight * 0.65;

            const bottomWindow = new HouseWindow([
                new art_Point/* Point */.E(bottomWindowX, bottomWindowY),
                new art_Point/* Point */.E(bottomWindowX + windowSize, bottomWindowY),
                new art_Point/* Point */.E(bottomWindowX + windowSize, bottomWindowY + windowSize),
                new art_Point/* Point */.E(bottomWindowX, bottomWindowY + windowSize)
            ]);

            windows.push(bottomWindow);
        } else {
            // For larger houses, create the pattern of 3 windows (1 on top, 2 on bottom)

            // Check if this is a very large house (grid width > 3)
            const isVeryLargeHouse = this.width / art_Art/* Art */.L.grid_dist > 3;

            // Adjust window size based on house size
            let topWindowSize = windowSize;
            let bottomWindowSize = windowSize;

            // For very large houses, make bottom windows 20% bigger
            if (isVeryLargeHouse) {
                bottomWindowSize = windowSize * 1.2;
            }

            // Adjust top window position based on house size
            // For very large houses, move the top window slightly lower
            const topWindowY = minY + wallHeight * (isVeryLargeHouse ? 0.35 : 0.25);

            // Create the top window (centered horizontally)
            const topWindowX = minX + (wallWidth - topWindowSize) / 2;

            const topWindow = new HouseWindow([
                new art_Point/* Point */.E(topWindowX, topWindowY),
                new art_Point/* Point */.E(topWindowX + topWindowSize, topWindowY),
                new art_Point/* Point */.E(topWindowX + topWindowSize, topWindowY + topWindowSize),
                new art_Point/* Point */.E(topWindowX, topWindowY + topWindowSize)
            ]);

            windows.push(topWindow);

            // Create the two bottom windows (evenly spaced in the lower third of the wall)
            const bottomWindowY = minY + wallHeight * 0.65; // Position in the lower part

            // Calculate positions for the two bottom windows
            const leftWindowX = minX + wallWidth * 0.25 - bottomWindowSize / 2;
            const rightWindowX = minX + wallWidth * 0.75 - bottomWindowSize / 2;

            // Create left bottom window
            const leftBottomWindow = new HouseWindow([
                new art_Point/* Point */.E(leftWindowX, bottomWindowY),
                new art_Point/* Point */.E(leftWindowX + bottomWindowSize, bottomWindowY),
                new art_Point/* Point */.E(leftWindowX + bottomWindowSize, bottomWindowY + bottomWindowSize),
                new art_Point/* Point */.E(leftWindowX, bottomWindowY + bottomWindowSize)
            ]);

            // Create right bottom window
            const rightBottomWindow = new HouseWindow([
                new art_Point/* Point */.E(rightWindowX, bottomWindowY),
                new art_Point/* Point */.E(rightWindowX + bottomWindowSize, bottomWindowY),
                new art_Point/* Point */.E(rightWindowX + bottomWindowSize, bottomWindowY + bottomWindowSize),
                new art_Point/* Point */.E(rightWindowX, bottomWindowY + bottomWindowSize)
            ]);

            windows.push(leftBottomWindow, rightBottomWindow);
        }

        return windows;
    }

        createWindowsForWall(wall) {
        const windows = [];

        // Get dimensions of the wall
        const wallWidth = wall.width();
        const wallHeight = wall.height();

        // Find the bounding box of the wall
        const xValues = wall.points.map(p => p.x);
        const yValues = wall.points.map(p => p.y);
        const minX = Math.min(...xValues);
        const minY = Math.min(...yValues);

        // Add wall margin - ensure windows don't touch the edges
        const wallMargin = art_Art/* Art */.L.grid_dist * 0.15;

        // Adjusted dimensions accounting for margins
        const availableWidth = wallWidth - (wallMargin * 2);
        const availableHeight = wallHeight - (wallMargin * 2);

        // Window parameters
        const windowSize = art_Art/* Art */.L.grid_dist * 0.35; // Slightly smaller
        const windowSpacing = art_Art/* Art */.L.grid_dist * 0.25;

        // Calculate how many rows and columns of windows to create
        // For houses with 2x2 dimensions, we'll aim for a single row of windows
        const numRows = Math.max(1, Math.min(2, Math.floor(availableHeight / (windowSize + windowSpacing))));
        const maxColumns = Math.floor(availableWidth / (windowSize + windowSpacing));
        const numColumns = Math.min(maxColumns, Math.max(1, Math.floor(wallWidth / art_Art/* Art */.L.grid_dist) + 1));

        // Calculate total width needed for all windows in a row
        const totalWindowWidth = numColumns * windowSize + (numColumns - 1) * windowSpacing;

        // Calculate horizontal starting position to center the windows
        const startX = minX + wallMargin + (availableWidth - totalWindowWidth) / 2;

        // Calculate vertical positions for the rows (evenly distributed)
        let rowPositions = [];
        if (numRows === 1) {
            // Single row centered vertically
            rowPositions.push(minY + wallMargin + (availableHeight - windowSize) / 2);
        } else {
            // Multiple rows with even spacing
            const rowSpacing = (availableHeight - (numRows * windowSize)) / (numRows + 1);
            for (let row = 0; row < numRows; row++) {
                rowPositions.push(minY + wallMargin + rowSpacing + row * (windowSize + rowSpacing));
            }
        }

        // Loop through rows and columns to create windows
        for (let row = 0; row < numRows; row++) {
            const y = rowPositions[row];

            for (let col = 0; col < numColumns; col++) {
                const x = startX + col * (windowSize + windowSpacing);

                // Create a potential window
                let windowCorners;
                if (this.type == "church") {
                    // Narrower windows for churches
                    const churchWindowWidth = windowSize * 0.33;
                    const xOffset = (windowSize - churchWindowWidth) / 2;
                    windowCorners = [
                        new art_Point/* Point */.E(x + xOffset, y),
                        new art_Point/* Point */.E(x + xOffset + churchWindowWidth, y),
                        new art_Point/* Point */.E(x + xOffset + churchWindowWidth, y + windowSize),
                        new art_Point/* Point */.E(x + xOffset, y + windowSize)
                    ];
                } else {
                    windowCorners = [
                        new art_Point/* Point */.E(x, y),
                        new art_Point/* Point */.E(x + windowSize, y),
                        new art_Point/* Point */.E(x + windowSize, y + windowSize),
                        new art_Point/* Point */.E(x, y + windowSize)
                    ];
                }

                // Check if window is inside the wall polygon with some buffer
                const isInside = this.isWindowInsideWall(windowCorners, wall.points);

                // Check if window overlaps with any door in the house
                const overlapsWithDoor = this.isWindowOverlappingDoor(windowCorners);

                // Only create the window if it's inside the wall and doesn't overlap with a door
                if (isInside && !overlapsWithDoor) {
                    const window = new HouseWindow(windowCorners);
                    windows.push(window);
                }
            }
        }

        return windows;
    }

    isWindowInsideWall(windowCorners, wallPoints) {
        // Create a temporary polygon from the wall points
        const wallPolygon = new art_Polygon.Polygon(wallPoints);

        // Check if all corners of the window are inside the wall polygon
        for (const corner of windowCorners) {
            if (!wallPolygon.containsPoint(corner)) {
                return false;
            }
        }
        return true;
    }

    isWindowOverlappingDoor(windowCorners) {
        // If the house doesn't have a door, there can't be any overlap
        if (!this.hasDoor) {
            return false;
        }

        // Find the bounding box of the window
        const windowMinX = Math.min(...windowCorners.map(p => p.x));
        const windowMaxX = Math.max(...windowCorners.map(p => p.x));
        const windowMinY = Math.min(...windowCorners.map(p => p.y));
        const windowMaxY = Math.max(...windowCorners.map(p => p.y));

        // Door parameters (based on createDoor method)
        const w = this.frontalWall.width();
        const doorWidth = art_Art/* Art */.L.grid_dist * 0.4 + art_Art/* Art */.L.rand() * 0.2; // Match the createDoor method
        const doorHeight = art_Art/* Art */.L.grid_dist * 0.7; // Match the createDoor method
        const doorX = this.point.x + w/2 - doorWidth/2;
        const doorY = this.point.y - doorHeight;

        // Check for overlap with door
        return (
            windowMaxX > doorX &&
            windowMinX < doorX + doorWidth &&
            windowMaxY > doorY &&
            windowMinY < doorY + doorHeight
        );
    }

    // Helper method to check if a point is inside a polygon
    //todo separar
    // This method is now in Polygon class as containsPoint

    createDoor() {
        // Use grid dimensions instead of percentages of the wall
        const doorWidth = art_Art/* Art */.L.grid_dist * 0.4+art_Art/* Art */.L.rand()*0.2;  // Door width based on grid
        const doorHeight = art_Art/* Art */.L.grid_dist * 0.7; // Door height based on grid

        // Center the door horizontally on the frontal wall
        let w = this.frontalWall.width();
        const doorX = this.point.x + w/2 - doorWidth/2;

        // Door starts from the bottom
        const doorY = this.point.y - doorHeight;

        let door=new HouseDoor([

            new art_Point/* Point */.E(doorX, doorY),
            new art_Point/* Point */.E(doorX + doorWidth, doorY),
            new art_Point/* Point */.E(doorX + doorWidth, doorY + doorHeight),
            new art_Point/* Point */.E(doorX, doorY + doorHeight)
        ]);
        door.style.fillStyle = this.color;
        door.style.strokeStyle = "#000000"; // Black stroke
        return door;
    }

    // createFills remains the same

    createFills() {
        //corregir perque nomes es fa una vegada

    }



    paint(){

        this.createFills();

        if(this.isFlipped){
         this.frontalWall.style.fillStyle = this.frontalWallFill;
        }else{
            this.frontalWall.style.fillStyle = this.frontalWallFill;
           // this.frontalWall.style.fillStyle = this.lateralWallFill;
        }

        if(this.lateralWallFill!=null){
            this.lateralWall.style.fillStyle = this.lateralWallFill;
        }else{
            this.lateralWall.style.fillStyle = this.wallFill;
        }

        if (this.polygons.length > 0) {
            // Calculate proportional line width based on layer's grid size
            const lineWidth = this.currentGridDist * 0.08*art_Art/* Art */.L.ratio;

            // Set line widths for all polygons
            for (let i = 0; i < this.polygons.length; i++) {
                // Determine which polygon this is (roof, walls, door)
                if (this.polygons[i].zIndex === 2) {
                    // This is the roof
                    if(window.roof_color==undefined){
                        window.roof_color=art_Art/* Art */.L.palette.getRandColor("roof");
                    }
                    let roof_color = window.roof_color

                    if (this.isFlipped) {
                        roof_color = art_Art/* Art */.L.palette.getColorDarkerEx(window.roof_color, this.sunIntensity*2);

                    }

                    this.polygons[i].polygon.setStyle({
                        fillStyle: roof_color,
                        strokeStyle: "#000",
                        lineWidth: lineWidth
                    });
                } else {
                    // For walls and door, just set the line width
                    this.polygons[i].polygon.style.strokeStyle = "#000";
                    this.polygons[i].polygon.style.lineWidth = lineWidth;
                }
            }

       
        }


        // Then call the parent paint method
        super.paint();
        this.paintFrontalWall();

        // Draw a base line
        art_Art/* Art */.L.setStyle({
            strokeStyle: "#000000",
            lineWidth: this.currentGridDist * 0.02
        });
        art_Art/* Art */.L.ctx.beginPath();
        art_Art/* Art */.L.moveTo(this.point.x-20, this.point.y);
        art_Art/* Art */.L.lineTo(this.point.x + 20 +this.width, this.point.y);
        art_Art/* Art */.L.ctx.stroke();


    }
    paintFrontalWall(){
        //nothing here

    }

    clone() {
        // Get bounds using the MultiPolygon's getBounds method
        const bounds = this.getBounds();

        // Calculate the original width and height
        const originalWidth = bounds.width;
        const originalHeight = this.point.y - bounds.minY; // Because point is at bottom

        // Create a new House with the correct dimensions
        const house = new this.constructor(
            new art_Point/* Point */.E(this.point.x, this.point.y),
            originalWidth/art_Art/* Art */.L.grid_dist,
            originalHeight/art_Art/* Art */.L.grid_dist,
            this.type
        );

        // If the original house was flipped, flip the cloned house too
        if (this.isFlipped) {
            house.flipHorizontal();
        }

        // Copy over any other properties if needed
        house.wallFill = this.wallFill;
        if (this.lateralWallFill) {
            house.lateralWallFill = this.lateralWallFill;
        }

        return house;
    }
}


class HouseWindow extends art_Polygon.Polygon {
    constructor(corners) {
        super(corners);
        // No need for type parameter as it's not used
        this.width = Math.max(
            Math.abs(this.points[1].x - this.points[0].x),
            Math.abs(this.points[2].x - this.points[3].x)
        );
        this.height = Math.max(
            Math.abs(this.points[3].y - this.points[0].y),
            Math.abs(this.points[2].y - this.points[1].y)
        );

        // Store current grid distance
        this.currentGridDist = art_Art/* Art */.L.grid_dist;
        // Calculate frame width proportionally
        this.frameWidth = this.currentGridDist * 0.0625; // 4/64 = 0.0625
        this.baseSubdivisionSize = art_Art/* Art */.L.grid_dist / 6;

        // Calculate number of subdivisions
        // Check if this is a larger window (20% larger than standard)
        const isLargerWindow = this.width > art_Art/* Art */.L.grid_dist * 0.35 * 1.1; // 10% threshold to detect larger windows

        if (isLargerWindow) {
            // For larger windows in frontal walls, use 2x2 panes
            this.numPanesX = 2;
            this.numPanesY = 2;
        } else {
            // For standard windows, calculate based on size
            this.numPanesX = Math.max(1, Math.round(this.width / this.baseSubdivisionSize));
            this.numPanesY = Math.max(1, Math.round(this.height / this.baseSubdivisionSize));
        }
    }

    paint() {
        // First fill all panes with black
        art_Art/* Art */.L.setStyle({
            fillStyle: "black"
        });

        for (let y = 0; y < this.numPanesY; y++) {
            for (let x = 0; x < this.numPanesX; x++) {
                const topLeft = this.interpolateRect(x, y);
                const topRight = this.interpolateRect(x + 1, y);
                const bottomLeft = this.interpolateRect(x, y + 1);
                const bottomRight = this.interpolateRect(x + 1, y + 1);

                // Fill the pane
                art_Art/* Art */.L.ctx.beginPath();
                art_Art/* Art */.L.moveTo(topLeft.x, topLeft.y);
                art_Art/* Art */.L.lineTo(topRight.x, topRight.y);
                art_Art/* Art */.L.lineTo(bottomRight.x, bottomRight.y);
                art_Art/* Art */.L.lineTo(bottomLeft.x, bottomLeft.y);
                art_Art/* Art */.L.ctx.closePath();
                art_Art/* Art */.L.ctx.fill();
            }
        }

        // Then draw all white frames
        art_Art/* Art */.L.setStyle({
            strokeStyle: "white",
            lineWidth: this.frameWidth*art_Art/* Art */.L.ratio
        });

        // Draw outer frame
        art_Art/* Art */.L.ctx.beginPath();
        art_Art/* Art */.L.moveTo(this.points[0].x, this.points[0].y);
        for (let i = 1; i < this.points.length; i++) {
            art_Art/* Art */.L.lineTo(this.points[i].x, this.points[i].y);
        }
        art_Art/* Art */.L.ctx.closePath();
        art_Art/* Art */.L.ctx.stroke();

        // Draw vertical dividers
        for (let i = 1; i < this.numPanesX; i++) {
            const ratio = i / this.numPanesX;
            const startPoint = this.points[0].getMiddle(this.points[1], ratio);
            const endPoint = this.points[3].getMiddle(this.points[2], ratio);

            art_Art/* Art */.L.ctx.beginPath();
            art_Art/* Art */.L.moveTo(startPoint.x, startPoint.y);
            art_Art/* Art */.L.lineTo(endPoint.x, endPoint.y);
            art_Art/* Art */.L.ctx.stroke();
        }

        // Draw horizontal dividers
        for (let i = 1; i < this.numPanesY; i++) {
            const ratio = i / this.numPanesY;
            const startPoint = this.points[0].getMiddle(this.points[3], ratio);
            const endPoint = this.points[1].getMiddle(this.points[2], ratio);

            art_Art/* Art */.L.ctx.beginPath();
            art_Art/* Art */.L.moveTo(startPoint.x, startPoint.y);
            art_Art/* Art */.L.lineTo(endPoint.x, endPoint.y);
            art_Art/* Art */.L.ctx.stroke();
        }

        art_Art/* Art */.L.restoreStyle();
    }

    // Update interpolateRect to use getMiddle
    //todo change
    interpolateRect(x, y) {
        const xRatio = x / this.numPanesX;
        const yRatio = y / this.numPanesY;

        const topPoint = this.points[0].getMiddle(this.points[1], xRatio);
        const bottomPoint = this.points[3].getMiddle(this.points[2], xRatio);
        return topPoint.getMiddle(bottomPoint, yRatio);
    }

    // This method is now in the Polygon class
}


class HouseDoor extends art_Polygon.Polygon {
    constructor(corners) {
        super(corners);
        // Store current grid distance
        this.currentGridDist = art_Art/* Art */.L.grid_dist;
        // Calculate frame width proportionally
        this.frameWidth = this.currentGridDist * 0.0625; // 4/64 = 0.0625
    }

    paint() {
        // First paint the door fill
        art_Art/* Art */.L.setStyle({
            fillStyle: this.style.fillStyle
        });

        art_Art/* Art */.L.ctx.beginPath();
        art_Art/* Art */.L.moveTo(this.points[0].x, this.points[0].y);
        for (let i = 1; i < this.points.length; i++) {
            art_Art/* Art */.L.lineTo(this.points[i].x, this.points[i].y);
        }
        art_Art/* Art */.L.ctx.closePath();
        art_Art/* Art */.L.ctx.fill();

        // Then draw white frame (only top and sides)
        art_Art/* Art */.L.setStyle({
            strokeStyle: "white",
            lineWidth: this.frameWidth*art_Art/* Art */.L.ratio
        });

        // Draw frame in a single continuous path
        art_Art/* Art */.L.ctx.beginPath();
        // Start from bottom-left
        art_Art/* Art */.L.moveTo(this.points[3].x, this.points[3].y);
        // Draw up to top-left
        art_Art/* Art */.L.lineTo(this.points[0].x, this.points[0].y);
        // Draw to top-right
        art_Art/* Art */.L.lineTo(this.points[1].x, this.points[1].y);
        // Draw down to bottom-right
        art_Art/* Art */.L.lineTo(this.points[2].x, this.points[2].y);

        art_Art/* Art */.L.ctx.stroke();
        art_Art/* Art */.L.restoreStyle();
    }
}

;// CONCATENATED MODULE: ./src/art/SwedishHouse.js








class SwedishHouse extends House {
    constructor(point, width, height, type="house") {
        super(point, width, height, type);

        // Randomly decide whether to add a chimney (10% chance)
        this.hasChimney = art_Art/* Art */.L.rand() < 0.1;
    }


    /**
     * Desaturate a color by a given amount
     * @param {string} color - The color to desaturate in hex format (#RRGGBB)
     * @param {number} amount - Amount to desaturate (0-1)
     * @returns {string} - The desaturated color in hex format
     */
    desaturateColor(color, amount) {
        // Convert hex to RGB
        const r = parseInt(color.substring(1, 3), 16) / 255;
        const g = parseInt(color.substring(3, 5), 16) / 255;
        const b = parseInt(color.substring(5, 7), 16) / 255;

        // Calculate luminance (perceived brightness)
        const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

        // Interpolate between original color and grayscale based on amount
        const newR = r + amount * (luminance - r);
        const newG = g + amount * (luminance - g);
        const newB = b + amount * (luminance - b);

        // Convert back to hex
        const toHex = (c) => {
            const hex = Math.round(c * 255).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };

        return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
    }

    static getHouse(w, p, layerIndex){ // de 2 a 6

    // Choose a random height based on width constraints
    let possibleHeights;

    // Set possible heights based on width (w)
    switch (w) {
        case 2:
            possibleHeights = [2, 3];
            break;
        case 3:
            possibleHeights = [2, 3];
            break;
        case 4:
            possibleHeights = [2, 3, 4];
            break;
        case 5:
            possibleHeights = [2, 3, 4];
            break;
        case 6:
            possibleHeights = [2, 3, 4, 5];
            break;
        case 7:
            possibleHeights = [2, 3, 4, 5];
            break;
        case 8:
            possibleHeights = [3, 4, 5, 6];
            break;
        default:
            // For any other width, use a reasonable range
            possibleHeights = [Math.max(2, Math.floor(w * 0.5)), Math.min(6, Math.ceil(w * 0.75))];
    }

    // Choose a random height from the possible range
    const h = Tools.getRandFromArray(possibleHeights);

        //if(h>w*2) h=w*1.25
        const house = new SwedishHouse(
            p,
            w,
            h
        );

        // Store the layer index for desaturation in createFills
        if (layerIndex !== undefined) {
            house.layerIndex = layerIndex;
        }

        if(art_Art/* Art */.L.rand()<0.5){
            house.flipHorizontal();
        }
        return house;

    }


    // Create striped pattern fills for the walls
    createFills() {
        // Get a base color for the house
        let baseColor = art_Art/* Art */.L.palette.getRandColor("house", this.point.x, this.point.y, this.currentGridDist);

        // Adjust saturation based on layer index (if provided)
        if (this.layerIndex !== undefined) {
            // Calculate desaturation factor based on layer index
            // Further layers (higher index) get more desaturated
            // Layer 0 is closest, higher indices are further away
            const desaturationFactor = this.layerIndex * 0.1; // 15% desaturation per layer

            // Desaturate the base color
            baseColor = this.desaturateColor(baseColor, desaturationFactor);
        }

        // Create a more subtle striped pattern for the walls
        // Use this.currentGridDist instead of Art.grid_dist to ensure proper scaling with house size
        const stripeWidth = this.currentGridDist * 0.2; // Width proportional to the house's grid distance
        const stripeHeight = this.currentGridDist * 0.4; // Height proportional to the house's grid distance

        // Create a canvas for the pattern
        const canvas = document.createElement('canvas');

        // Ensure the canvas dimensions are valid (at least 1 pixel)
        const canvasWidth = Math.max(1, stripeWidth * 2 * art_Art/* Art */.L.ratio);
        const canvasHeight = Math.max(1, stripeHeight * art_Art/* Art */.L.ratio);

        canvas.width = canvasWidth;
        canvas.height = canvasHeight;

        // Get the canvas context
        const ctx = canvas.getContext('2d');

        // Save the current Art context and set it to our pattern canvas
        art_Art/* Art */.L.setCtx(ctx);

        // Create an extremely subtle darker color for alternating stripes (minimal contrast)
        let darkerColor = art_Art/* Art */.L.palette.getColorDarkerEx(baseColor, 0.04); // Very minimal darkening for subtle effect

        // Draw the first stripe (base color)
        ctx.fillStyle = baseColor;
        art_Art/* Art */.L.fillRect(0, 0, stripeWidth, stripeHeight);

        // Draw the second stripe (slightly darker color)
        ctx.fillStyle = darkerColor;
        art_Art/* Art */.L.fillRect(stripeWidth, 0, stripeWidth, stripeHeight);

        // Make the dividing line between stripes extremely subtle
        ctx.strokeStyle = art_Art/* Art */.L.palette.getColorDarkerEx(baseColor, 0.05); // Very slight darkening
        ctx.lineWidth = 0.3 * art_Art/* Art */.L.ratio; // Extremely thin line
        ctx.globalAlpha = 0.15; // Very low opacity for maximum subtlety
        ctx.beginPath();
        art_Art/* Art */.L.moveTo(stripeWidth, 0);
        art_Art/* Art */.L.lineTo(stripeWidth, stripeHeight);
        ctx.stroke();
        ctx.globalAlpha = 1.0; // Reset alpha

        // Restore the original Art context
        art_Art/* Art */.L.restoreCtx();

        // Create the pattern from the canvas
        this.wallFill = ctx.createPattern(canvas, 'repeat');

        // Create adjusted versions for frontal and lateral walls based on lighting
        if (this.isFlipped) {
            // When flipped, the frontal wall gets more light, lateral wall gets less
            this.frontalWallFill = this.wallFill; // Use pattern directly for frontal wall

            // For the lateral wall, create a darker version of the pattern
            const lateralCanvas = document.createElement('canvas');
            lateralCanvas.width = Math.max(1, canvas.width);
            lateralCanvas.height = Math.max(1, canvas.height);
            const lateralCtx = lateralCanvas.getContext('2d');

            // Draw the original pattern with a very subtle dark overlay
            lateralCtx.fillStyle = this.wallFill;
            lateralCtx.fillRect(0, 0, lateralCanvas.width, lateralCanvas.height);
            lateralCtx.fillStyle = 'rgba(0, 0, 0, ' + (this.sunIntensity * 0.7) + ')';
            lateralCtx.fillRect(0, 0, lateralCanvas.width, lateralCanvas.height);

            this.lateralWallFill = lateralCtx.createPattern(lateralCanvas, 'repeat');
        } else {
            // When not flipped, the lateral wall gets more light, frontal wall gets less
            this.lateralWallFill = this.wallFill; // Use pattern directly for lateral wall

            // For the frontal wall, create a darker version of the pattern
            const frontalCanvas = document.createElement('canvas');
            frontalCanvas.width = Math.max(1, canvas.width);
            frontalCanvas.height = Math.max(1, canvas.height);
            const frontalCtx = frontalCanvas.getContext('2d');

            // Draw the original pattern with a very subtle dark overlay
            frontalCtx.fillStyle = this.wallFill;
            frontalCtx.fillRect(0, 0, frontalCanvas.width, frontalCanvas.height);
            frontalCtx.fillStyle = 'rgba(0, 0, 0, ' + (this.sunIntensity * 0.7) + ')';
            frontalCtx.fillRect(0, 0, frontalCanvas.width, frontalCanvas.height);

            this.frontalWallFill = frontalCtx.createPattern(frontalCanvas, 'repeat');
        }
    }


    paintFrontalWall(){
        super.paintFrontalWall();





       // frontMargin.paint()
    }

    createMoreStuff(){

        const margin=art_Art/* Art */.L.grid_dist*0.15
        let  p1=this.frontalWall.getPoint(0).move(margin,0)
        let p2=this.frontalWall.getPoint(1).move(0,margin*1.5)
        let p3=this.frontalWall.getPoint(2).move(-margin,0)

        const frontMargin=new art_Polygon.Polygon(
            [p1,this.frontalWall.getPoint(0),this.frontalWall.getPoint(1),this.frontalWall.getPoint(2),p3,p2]
        )
        frontMargin.setStyle({
            fillStyle: '#fff',

        });

        this.addPolygon(frontMargin, 1);
    }

}

class SwedishChurch extends SwedishHouse{
    // Static constants for roof configuration
    static ROOF_BOTTOM_HEIGHT_PROP = 0.15; // 15% of total roof height
    static ROOF_MIDDLE_HEIGHT_PROP = 0.25; // 25% of total roof height
    static ROOF_TOP_HEIGHT_PROP = 0.6;     // 60% of total roof height

    // Static constants for roof colors
    static ROOF_COLOR = "#4A4A4A";         // Dark gray for the main roof
    static TOWER_COLOR = "#333333";        // Darker gray for the tower/spire


    constructor(point, width,type="church") {
        let height=3
        if(width==3) height=2.25
        if(width==4) height=3
        if(width==4 && type=="dom") height=5
        if(width==5) height=3.8
        if(width==6) height=4.6
        super(point, width, height, "church");

        // Ensure currentGridDist is set (needed for createFills)
        this.currentGridDist = art_Art/* Art */.L.grid_dist;

        // Calculate total roof height
        const hw = 0.5; // Base height factor
        const ht = 2; // Total height factor
        const totalRoofHeight = this.height * ht - this.height * hw;

        // Calculate actual heights for each level using static constants
        const bottomHeight = totalRoofHeight * SwedishChurch.ROOF_BOTTOM_HEIGHT_PROP;
        const middleHeight = totalRoofHeight * SwedishChurch.ROOF_MIDDLE_HEIGHT_PROP;
        // Top height is determined by the remaining space

        // Calculate y-coordinates for each level
        const baseY = this.point.y - this.height * hw; // Base of the roof
        const bottomLevelY = baseY - bottomHeight;
        const middleLevelY = bottomLevelY - middleHeight;
        const topLevelY = this.point.y - this.height * ht; // Top of the roof (peak)

        // Calculate x-coordinates
        const leftX = this.point.x;
        const rightX = this.point.x + this.width / 2;
        const peakX = this.point.x + this.width / 4;

        // Calculate intermediate points for the tapered shape
        const bottomWidthFactor = 0.15; // How much the roof narrows at the bottom level

        // Calculate intermediate x-coordinates
        const bottomLeftX = leftX + (peakX - leftX) * bottomWidthFactor;
        const bottomRightX = rightX - (rightX - peakX) * bottomWidthFactor;

        // For vertical middle walls, use the same x-coordinates as the bottom level
        const middleLeftX = bottomLeftX;
        const middleRightX = bottomRightX;

        // For a sharper top, make the top section narrower
        const topWidthFactor = 0.6; // Higher value = sharper top
        const topLeftX = middleLeftX + (peakX - middleLeftX) * topWidthFactor;
        const topRightX = middleRightX - (middleRightX - peakX) * topWidthFactor;

        // Create the bottom level of the roof (tapered)
        const roofBottom = new art_Polygon.Polygon([
            new art_Point/* Point */.E(leftX, baseY),
            new art_Point/* Point */.E(bottomLeftX, bottomLevelY),
            new art_Point/* Point */.E(bottomRightX, bottomLevelY),
            new art_Point/* Point */.E(rightX, baseY)
        ]);

        // Create the middle level of the roof (vertical walls)
        const roofMiddle = new art_Polygon.Polygon([
            new art_Point/* Point */.E(bottomLeftX, bottomLevelY),
            new art_Point/* Point */.E(middleLeftX, middleLevelY),
            new art_Point/* Point */.E(middleRightX, middleLevelY),
            new art_Point/* Point */.E(bottomRightX, bottomLevelY)
        ]);

        // Create the top level of the roof (sharper peak)
        const roofTop = new art_Polygon.Polygon([
            new art_Point/* Point */.E(middleLeftX, middleLevelY),
            new art_Point/* Point */.E(topLeftX, middleLevelY - (middleLevelY - topLevelY) * 0.3),
            new art_Point/* Point */.E(peakX, topLevelY),
            new art_Point/* Point */.E(topRightX, middleLevelY - (middleLevelY - topLevelY) * 0.3),
            new art_Point/* Point */.E(middleRightX, middleLevelY)
        ]);


        // Use our static color constants instead of window.roof_color
        const roofColor = SwedishChurch.ROOF_COLOR;
        const strokeWidth = art_Art/* Art */.L.grid_dist * 0.02;

        // Set styles for each roof level with slightly different shades for visual interest
        roofBottom.setStyle({
            fillStyle: roofColor,
            strokeStyle: "#000000",
            lineWidth: strokeWidth
        });

        // Middle roof - slightly darker for visual separation
        const middleRoofColor = this.color//Art.palette.getColorDarkerEx(roofColor, 0.1);
        roofMiddle.setStyle({
            fillStyle: middleRoofColor,
            strokeStyle: "#000000",
            lineWidth: strokeWidth
        });

        // Top roof - even darker for better visual hierarchy
        const topRoofColor = this.color//Art.palette.getColorDarkerEx(roofColor, 0.2);
        roofTop.setStyle({
            fillStyle: topRoofColor,
            strokeStyle: "#000000",
            lineWidth: strokeWidth
        });

        // Add the roof levels to the church with appropriate z-indices
        this.addPolygon(roofBottom, 2); // Bottom level
        this.addPolygon(roofMiddle, 3); // Middle level
        this.addPolygon(roofTop, 4);    // Top level (peak)

        // Store references for later use
        this.roofBottom = roofBottom;
        this.roofMiddle = roofMiddle;
        this.roofTop = roofTop;

        // Store the roof colors for use in other methods
        this.roof_color = roofColor; // For compatibility with existing code
        this.tower_color = SwedishChurch.TOWER_COLOR;
        this.cross_color = SwedishChurch.CROSS_COLOR;

        // Calculate and store the clock position (center of the middle roof section)
        this.clockX = (middleLeftX + middleRightX) / 2;
        this.clockY = (bottomLevelY + middleLevelY) / 2;
        // Make the clock half size, and ensure it's never bigger than the middle roof
        this.clockRadius = Math.min(middleHeight, (middleRightX - middleLeftX) / 2) * 0.35; // 35% of the available space (half of 70%)
    }

    paint(){
        // If we have a layer index, make sure to apply it before painting
        if (this.layerIndex !== undefined && !this.fillsCreated) {
            // Ensure the createFills method will use the layer index for desaturation
            // This is needed because SwedishChurch extends SwedishHouse
            try {
                this.createFills();
                this.fillsCreated = true; // Mark fills as created to avoid redundant calls
            } catch (error) {
                console.error('Error creating fills for church:', error);
                // Set default fills to avoid further errors
                this.wallFill = this.frontalWallFill = this.lateralWallFill = '#FFFFFF';
            }
        }

        super.paint();

        // Draw the clock on the middle roof
        this.drawClock();

        // Draw the cross at the top of the church
        this.drawCross();
    }

    // Override the flipHorizontal method to update the clock position
    flipHorizontal() {
        // Call the parent class's flipHorizontal method
        super.flipHorizontal();

        // Update the clock position if it exists
        if (this.clockX !== undefined && this.clockY !== undefined) {
            // Calculate the width of the church
            const width = this.width;

            // Calculate the new clock position
            // When flipped, the clock should be on the opposite side
            // The formula is: newX = 2 * centerX - oldX
            // Where centerX is the center of the church (this.point.x + width/2)
            const centerX = this.point.x + width/2;
            this.clockX = 2 * centerX - this.clockX;
        }
    }

    drawClock() {
        // Check if clock position is defined
        if (!this.clockX || !this.clockY) return;

        // Calculate clock radius - make it smaller (half size, never bigger than the middle roof)
        // Get the width of the middle roof section
        const middleRoofWidth = this.roofMiddle.getBounds().width;
        // Make the clock radius proportional to the middle roof width, but not too large
        this.clockRadius = Math.min(middleRoofWidth * 0.25, this.height * 0.2);

        // Get the current context
        const ctx = art_Art/* Art */.L.ctx;

        // Save current context state
        ctx.save();

        // Create a subtle shadow for depth
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 5;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;

        // Draw clock face - simple circle with a subtle gradient for dimension
        const gradient = ctx.createRadialGradient(
            this.clockX, this.clockY, this.clockRadius * 0.1,
            this.clockX, this.clockY, this.clockRadius
        );
        gradient.addColorStop(0, "#FFFFFF");
        gradient.addColorStop(1, "#F0F0F0");

        ctx.beginPath();
        ctx.arc(this.clockX, this.clockY, this.clockRadius, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();

        // Add a subtle outer ring
        ctx.strokeStyle = "#333333";
        ctx.lineWidth = art_Art/* Art */.L.grid_dist * 0.02;
        ctx.stroke();

        // Remove shadow for the clock details
        ctx.shadowColor = 'transparent';

        // Draw simplified hour markers - just 4 main markers (12, 3, 6, 9)
        ctx.lineWidth = art_Art/* Art */.L.grid_dist * 0.015;
        for (let i = 0; i < 4; i++) {
            const angle = (i * Math.PI / 2); // 0, 90, 180, 270 degrees
            const markerLength = this.clockRadius * 0.15;
            const startRadius = this.clockRadius * 0.75;

            ctx.beginPath();
            ctx.moveTo(
                this.clockX + Math.sin(angle) * startRadius,
                this.clockY - Math.cos(angle) * startRadius
            );
            ctx.lineTo(
                this.clockX + Math.sin(angle) * (startRadius + markerLength),
                this.clockY - Math.cos(angle) * (startRadius + markerLength)
            );
            ctx.stroke();
        }

        // Get current time
        const now = new Date();
        const hours = now.getHours() % 12;
        const minutes = now.getMinutes();

        // Calculate angles for clock hands
        let hourAngle = (hours + minutes / 60) * Math.PI / 6;
        let minuteAngle = minutes * Math.PI / 30;

        // We want both clocks to show the same time, so we don't adjust the angles
        // for flipped churches anymore. This ensures both clocks show the same time.

        // Draw hour hand - shorter and thicker
        ctx.beginPath();
        ctx.moveTo(this.clockX, this.clockY);
        ctx.lineTo(
            this.clockX + Math.sin(hourAngle) * this.clockRadius * 0.5,
            this.clockY - Math.cos(hourAngle) * this.clockRadius * 0.5
        );
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = art_Art/* Art */.L.grid_dist * 0.03;
        ctx.stroke();

        // Draw minute hand - longer and thinner
        ctx.beginPath();
        ctx.moveTo(this.clockX, this.clockY);
        ctx.lineTo(
            this.clockX + Math.sin(minuteAngle) * this.clockRadius * 0.7,
            this.clockY - Math.cos(minuteAngle) * this.clockRadius * 0.7
        );
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = art_Art/* Art */.L.grid_dist * 0.015;
        ctx.stroke();

        // Draw center dot with a metallic look
        const centerGradient = ctx.createRadialGradient(
            this.clockX, this.clockY, 0,
            this.clockX, this.clockY, this.clockRadius * 0.08
        );
        centerGradient.addColorStop(0, "#666666");
        centerGradient.addColorStop(0.5, "#333333");
        centerGradient.addColorStop(1, "#111111");

        ctx.beginPath();
        ctx.arc(this.clockX, this.clockY, this.clockRadius * 0.08, 0, Math.PI * 2);
        ctx.fillStyle = centerGradient;
        ctx.fill();

        // Add a small highlight to the center dot
        ctx.beginPath();
        ctx.arc(
            this.clockX - this.clockRadius * 0.02,
            this.clockY - this.clockRadius * 0.02,
            this.clockRadius * 0.02,
            0, Math.PI * 2
        );
        ctx.fillStyle = "rgba(255, 255, 255, 0.7)";
        ctx.fill();

        // Restore context state
        ctx.restore();
    }

    drawCross() {
        // Calculate the position for the cross (at the peak of the roof)
        let crossX = this.point.x + this.width/4;
        if(this.isFlipped){
            crossX = this.point.x + this.width*(3/4);
        }
        const crossY = this.point.y - this.height*2;

        const crossWidth = this.width * 0.1;
        const crossHeight = this.height * 0.2;
        const circleRadius = crossWidth * 0.3;

        // Use the same roof color that was defined in the constructor

        // Create the circle at the beginning of the cross
        const circle = new Ellipse(crossX, crossY, circleRadius, circleRadius, art_Art/* Art */.L.dist/4);
        circle.setStyle({
            fillStyle: this.roof_color,
            strokeStyle: "#000000",
            lineWidth: 2*art_Art/* Art */.L.grid_dist*0.02
        });
        circle.paint();

        // Get the current context
        const ctx = art_Art/* Art */.L.ctx;

        // Set cross style
        this.setStyle({
            "strokeStyle":"#000000",
            "lineWidth":2*art_Art/* Art */.L.grid_dist*0.02
        });

        // Draw the vertical line of the cross
        ctx.beginPath();
        art_Art/* Art */.L.moveTo(crossX, crossY);
        art_Art/* Art */.L.lineTo(crossX, crossY - crossHeight);

        // Draw the horizontal line of the cross
        art_Art/* Art */.L.moveTo(crossX - crossWidth/2, crossY - crossHeight/2);
        art_Art/* Art */.L.lineTo(crossX + crossWidth/2, crossY - crossHeight/2);

        // Render the cross
        ctx.stroke();
    }

}

class SwedishDom{
    constructor(point) {
        let roof_angle = House.roof_angle;
        House.roof_angle = 2.5;

        // Store the current grid distance for scaling
        this.currentGridDist = art_Art/* Art */.L.grid_dist;

        // Create the left church
        this.dom1 = new SwedishChurch(
            new art_Point/* Point */.E(point.x, point.y),
            4,
            "dom"
        );

        // Create the right church
        this.dom2 = new SwedishChurch(
            new art_Point/* Point */.E(point.x + 4 * art_Art/* Art */.L.grid_dist, point.y),
            4,
            "dom"
        );

        // Call flipHorizontal to actually perform the flipping
        this.dom1.flipHorizontal();

        // Store the base point for the tower
        this.point = point;

        // Restore the original roof angle
        House.roof_angle = roof_angle;
    }

    paint(){
        // Paint the central tower first so it appears behind the churches
        this.paintCentralTower();

        // Then paint the two churches on top
        this.dom1.paint();
        this.dom2.paint();
    }

    paintCentralTower() {
        // Get the current context
        const ctx = art_Art/* Art */.L.ctx;

        // Save current context state
        ctx.save();

        // Calculate tower dimensions and position
        const towerBaseWidth = 3.5 * this.currentGridDist; // Slightly wider base
        const towerHeight = 14 * this.currentGridDist; // Taller tower
        const towerRectHeight = 5 * this.currentGridDist; // Height of the rectangular part

        // Position the tower in the middle of the two churches horizontally
        const towerBaseX = this.point.x + 4 * this.currentGridDist - towerBaseWidth / 2;

        // Position the tower at the same level as the churches (the floor of the church)
        const towerBaseY = this.point.y;

        // Create a shadow for depth
        ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetX = 5;
        ctx.shadowOffsetY = 5;

        // Draw the rectangular base of the tower
        const baseColor = SwedishChurch.TOWER_COLOR || "#333333"; // Dark gray
        ctx.fillStyle = baseColor;
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = this.currentGridDist * 0.02;

        // Rectangular base
        ctx.beginPath();
        ctx.rect(
            towerBaseX * art_Art/* Art */.L.ratio,
            (towerBaseY - towerRectHeight) * art_Art/* Art */.L.ratio,
            towerBaseWidth * art_Art/* Art */.L.ratio,
            towerRectHeight * art_Art/* Art */.L.ratio
        );
        ctx.fill();
        ctx.stroke();

        // Remove shadow for details
        ctx.shadowColor = 'transparent';

        // Add windows to the tower base
        this.drawTowerWindows(towerBaseX, towerBaseY, towerBaseWidth, towerRectHeight);

        // Restore shadow for the spire
        ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetX = 5;
        ctx.shadowOffsetY = 5;

        // Draw the tower spire (tall triangle)
        // Use a darker color for the spire than the base
        const spireColor = art_Art/* Art */.L.palette.getColorDarkerEx(baseColor, 0.2);
        ctx.fillStyle = spireColor;

        // Calculate spire dimensions
        const spireBaseWidth = towerBaseWidth * 0.9; // Slightly narrower than the base
        const spireBaseX = towerBaseX + (towerBaseWidth - spireBaseWidth) / 2;
        const spireBaseY = towerBaseY - towerRectHeight;
        const spireHeight = towerHeight - towerRectHeight;

        // Draw the spire
        ctx.beginPath();
        // Bottom left
        ctx.moveTo(spireBaseX * art_Art/* Art */.L.ratio, spireBaseY * art_Art/* Art */.L.ratio);
        // Top point
        ctx.lineTo((spireBaseX + spireBaseWidth / 2) * art_Art/* Art */.L.ratio, (spireBaseY - spireHeight) * art_Art/* Art */.L.ratio);
        // Bottom right
        ctx.lineTo((spireBaseX + spireBaseWidth) * art_Art/* Art */.L.ratio, spireBaseY * art_Art/* Art */.L.ratio);
        // Close the path
        ctx.closePath();
        ctx.fill();
        ctx.stroke();

        // Draw a gold cross at the very top
        this.drawTowerCross(spireBaseX + spireBaseWidth / 2, spireBaseY - spireHeight);

        // Restore context state
        ctx.restore();
    }

    // Draw arched windows on the tower
    drawTowerWindows(towerBaseX, towerBaseY, towerWidth, towerHeight) {
        const ctx = art_Art/* Art */.L.ctx;

        // Window parameters
        const windowWidth = towerWidth * 0.2;
        const windowHeight = towerHeight * 0.4;
        const archHeight = windowHeight * 0.3;
        const windowSpacing = towerWidth * 0.1;

        // Calculate positions for three windows
        const startX = towerBaseX + (towerWidth - (3 * windowWidth + 2 * windowSpacing)) / 2;
        const windowY = towerBaseY - towerHeight + towerHeight * 0.3;

        // Draw three arched windows
        for (let i = 0; i < 3; i++) {
            const windowX = startX + i * (windowWidth + windowSpacing);

            // Draw the window with a dark blue color for stained glass effect
            ctx.fillStyle = "#000033";

            // Draw the rectangular part
            ctx.beginPath();
            ctx.rect(
                windowX * art_Art/* Art */.L.ratio,
                windowY * art_Art/* Art */.L.ratio,
                windowWidth * art_Art/* Art */.L.ratio,
                (windowHeight - archHeight) * art_Art/* Art */.L.ratio
            );
            ctx.fill();
            ctx.stroke();

            // Draw the arch
            ctx.beginPath();
            ctx.arc(
                (windowX + windowWidth / 2) * art_Art/* Art */.L.ratio,
                (windowY + windowHeight - archHeight) * art_Art/* Art */.L.ratio,
                (windowWidth / 2) * art_Art/* Art */.L.ratio,
                Math.PI, 0, false
            );
            ctx.fill();
            ctx.stroke();
        }
    }

    drawTowerCross(x, y) {
        // Get the current context
        const ctx = art_Art/* Art */.L.ctx;

        // Cross parameters - match the church cross dimensions
        const crossWidth = this.currentGridDist * 0.4;
        const crossHeight = this.currentGridDist * 0.8;
        const circleRadius = crossWidth * 0.3;

        // Use the same roof color for consistency
        const crossColor = window.roof_color || "#8B4513";

        // Create the circle at the beginning of the cross (like the church cross)
        ctx.beginPath();
        ctx.arc(
            x * art_Art/* Art */.L.ratio,
            y * art_Art/* Art */.L.ratio,
            circleRadius * art_Art/* Art */.L.ratio,
            0,
            Math.PI * 2
        );
        ctx.fillStyle = crossColor;
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = 2 * this.currentGridDist * 0.02;
        ctx.fill();
        ctx.stroke();

        // Set cross style
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = 2 * this.currentGridDist * 0.02;

        // Draw the vertical line of the cross
        ctx.beginPath();
        ctx.moveTo(x * art_Art/* Art */.L.ratio, y * art_Art/* Art */.L.ratio);
        ctx.lineTo(x * art_Art/* Art */.L.ratio, (y - crossHeight) * art_Art/* Art */.L.ratio);

        // Draw the horizontal line of the cross
        ctx.moveTo((x - crossWidth/2) * art_Art/* Art */.L.ratio, (y - crossHeight/2) * art_Art/* Art */.L.ratio);
        ctx.lineTo((x + crossWidth/2) * art_Art/* Art */.L.ratio, (y - crossHeight/2) * art_Art/* Art */.L.ratio);

        // Render the cross
        ctx.stroke();
    }
}
;// CONCATENATED MODULE: ./src/art/Noise.js


// Self-contained Perlin noise implementation
class Noise {
    constructor(seed = art_Art/* Art */.L.rand() * 10000) {
        this.seed(seed);
        this.cx = Math.floor(art_Art/* Art */.L.rand() * 10000);
        this.cy = Math.floor(art_Art/* Art */.L.rand() * 10000);
    }
    
    seed(seed) {
        this.p = new Array(512);
        const permutation = new Array(256);
        
        // Initialize array with values 0-255
        for (let i = 0; i < 256; i++) {
            permutation[i] = i;
        }
        
        // Create seeded random function
        let n = 256;
        let randomFunc = () => {
            seed = (seed * 16807) % 2147483647;
            return seed / 2147483647;
        };
        
        // Shuffle permutation array using Fisher-Yates
        while (n > 0) {
            const i = Math.floor(randomFunc() * n--);
            const t = permutation[n];
            permutation[n] = permutation[i];
            permutation[i] = t;
        }
        
        // Copy into expanded array
        for (let i = 0; i < 512; i++) {
            this.p[i] = permutation[i & 255];
        }
    }
    
    fade(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    lerp(t, a, b) {
        return a + t * (b - a);
    }
    
    grad(hash, x, y, z) {
        const h = hash & 15;
        const u = h < 8 ? x : y;
        const v = h < 4 ? y : (h === 12 || h === 14) ? x : z;
        return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
    }
    
    noise2D(x, y, resolution = 0.01) {
        x=x+resolution
        y=y+resolution
        
        x = (x + this.cx) * resolution;
        y = (y + this.cy) * resolution;
        
        const X = Math.floor(x) & 255;
        const Y = Math.floor(y) & 255;
        
        x -= Math.floor(x);
        y -= Math.floor(y);
        
        const u = this.fade(x);
        const v = this.fade(y);
        
        const A = this.p[X] + Y;
        const AA = this.p[A];
        const AB = this.p[A + 1];
        const B = this.p[X + 1] + Y;
        const BA = this.p[B];
        const BB = this.p[B + 1];
        
        return this.lerp(v, 
            this.lerp(u, 
                this.grad(this.p[AA], x, y, 0),
                this.grad(this.p[BA], x - 1, y, 0)
            ),
            this.lerp(u,
                this.grad(this.p[AB], x, y - 1, 0),
                this.grad(this.p[BB], x - 1, y - 1, 0)
            )
        );
    }
    
    // Fractal noise (multiple octaves)
    fractal2D(x, y, octaves = 4, lacunarity = 2.0, persistence = 0.5) {
        let total = 0;
        let frequency = 1;
        let amplitude = 1;
        let maxValue = 0;
        
        for (let i = 0; i < octaves; i++) {
            total += this.noise2D(x * frequency, y * frequency) * amplitude;
            
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return total / maxValue;
    }
}
;// CONCATENATED MODULE: ./src/art/Palette.js




class Palette {
    constructor(colors = {}) {
        this.colors = colors;
        this.noise= new Noise();
    }

    getColor(index,type=null) {
        if(type==null) type="main"
        let colors=this.colors[type]

         if(colors==null) return "#000000"
        if (index < 0 || index >= colors.length) {
            throw new Error('Index out of bounds');
        }
        return colors[index];
    }
      getRandColor(type=null, x=0, y=0,dist=50) {
  
        if(type==null) type="main";

        let colors=this.colors[type];
        if(colors==null) return "#000000";

        let selectedColor;
        if(x==0 && y==0){
            selectedColor = Tools.getRandFromArray(colors);
        } else {

            // Get noise value in range [-1, 1]

            dist=dist
           // const p=0.0001
            const p=1
            let noiseValue = this.noise.noise2D(x-art_Art/* Art */.L.width/2, y-art_Art/* Art */.L.height/2, p*dist*0.5);

            if(art_Art/* Art */.L.rand()<0.01)console.log("noiseValue",noiseValue)
            // Map from [-1, 1] to [0, colors.length-1]
            // First convert from [-1, 1] to [0, 1]
            let normalizedValue = (noiseValue + 1) / 2;

            // Then scale to [0, colors.length-1]
            let index = Math.floor(normalizedValue * colors.length);

            // Ensure index is within bounds
            index = Math.max(0, Math.min(colors.length - 1, index));

            selectedColor = colors[index];
        }
        //param mode fosc i clar i de color (modes de color) //todo
        switch($fx.getParam("color_mode")){
            case "black":
                return "#000000";
            case "white":
                return "#ffffff";
            case "b&w":
                return art_Art/* Art */.L.rand()<0.5?"#000000":"#ffffff";
            case "color":
                if(window.monocolor==undefined){
                    window.monocolor=Tools.getRandFromArray(this.colors["house"])
                }else{
                    return window.monocolor;
                }
                
            default:
                return selectedColor;
        }

        //selectedColor ='#000000'
       // if(Art.rand()<0.5) selectedColor='#666666'

        // Apply day_time modifications to the selected color
        return this.applyDayTimeModification(selectedColor, type);
    }

    /**
     * Apply day_time modifications to colors based on the $fx.getParam("day_time") value
     * @param {string} color - The original color in hex format
     * @param {string} type - The color type (sky, house, landscape, roof, etc.)
     * @returns {string} - The modified color based on day_time
     */
    applyDayTimeModification(color, type) {
        // Validate input color
        if (!color || typeof color !== 'string') {
            console.warn('Invalid color input to applyDayTimeModification:', color);
            return color || "#000000"; // Return original or fallback
        }

        // Get the day_time parameter from $fx
        const dayTime = typeof $fx !== 'undefined' ? $fx.getParam("day_time") : "day";

        // If it's day time, return the original color unchanged
        if (dayTime === "day") {
            return color;
        }

        // Define modification factors for different times and color types
        let darkeningFactor = 0;
        let colorShift = null;

        switch (dayTime) {
            case "morning":
                // Morning: slight warm tint and very slight darkening
                switch (type) {
                    case "sky":
                        // Morning sky gets a warm, golden tint
                        colorShift = { r: 1.1, g: 1.05, b: 0.9 }; // Warm golden tint
                        darkeningFactor = 0.1;
                        break;
                    case "house":
                    case "roof":
                    case "landscape":
                        // Other elements get slightly warmer and slightly darker
                        colorShift = { r: 1.05, g: 1.02, b: 0.95 };
                        darkeningFactor = 0.05;
                        break;
                    default:
                        darkeningFactor = 0.05;
                        break;
                }
                break;

            case "evening":
                // Evening: warm orange/red tint and moderate darkening
                switch (type) {
                    case "sky":
                        // Evening sky gets warm orange/red tint
                        colorShift = { r: 1.2, g: 0.9, b: 0.7 }; // Orange/red tint
                        darkeningFactor = 0.2;
                        break;
                    case "house":
                    case "roof":
                    case "landscape":
                        // Other elements get warmer and moderately darker
                        colorShift = { r: 1.1, g: 0.95, b: 0.8 };
                        darkeningFactor = 0.15;
                        break;
                    default:
                        darkeningFactor = 0.15;
                        break;
                }
                break;

            case "night":
                // Night: much darker, blue tint
                switch (type) {
                    case "sky":
                        // Night sky becomes much darker with blue tint
                        colorShift = { r: 0.3, g: 0.4, b: 0.8 }; // Blue tint
                        darkeningFactor = 0.8; // Much darker
                        break;
                    case "house":
                    case "roof":
                    case "landscape":
                        // Other elements become much darker with slight blue tint
                        colorShift = { r: 0.7, g: 0.75, b: 0.9 };
                        darkeningFactor = 0.6; // Pretty darker
                        break;
                    default:
                        darkeningFactor = 0.6;
                        break;
                }
                break;

            default:
                return color; // Unknown day_time, return original
        }

        // Apply the modifications
        let modifiedColor = color;

        // Apply color shift if specified
        if (colorShift) {
            modifiedColor = this.applyColorShift(modifiedColor, colorShift);
            // Validate the result
            if (!modifiedColor || modifiedColor.includes('NaN')) {
                console.warn('Color shift produced invalid result, using original:', color);
                modifiedColor = color;
            }
        }

        // Apply darkening
        if (darkeningFactor > 0) {
            modifiedColor = this.getColorDarkerEx(modifiedColor, darkeningFactor);
            // Validate the result
            if (!modifiedColor || modifiedColor.includes('NaN')) {
                console.warn('Color darkening produced invalid result, using original:', color);
                modifiedColor = color;
            }
        }

        return modifiedColor;
    }

    /**
     * Apply a color shift (tint) to a color
     * @param {string} color - The original color in hex format
     * @param {Object} shift - Object with r, g, b multipliers
     * @returns {string} - The shifted color
     */
    applyColorShift(color, shift) {
        // Validate input color
        if (!color || typeof color !== 'string' || !color.startsWith('#') || color.length !== 7) {
            console.warn('Invalid color format:', color);
            return color; // Return original if invalid
        }

        // Convert hex to RGB with validation
        let r = parseInt(color.slice(1, 3), 16);
        let g = parseInt(color.slice(3, 5), 16);
        let b = parseInt(color.slice(5, 7), 16);

        // Check for NaN values
        if (isNaN(r) || isNaN(g) || isNaN(b)) {
            console.warn('Failed to parse color:', color);
            return color; // Return original if parsing failed
        }

        // Validate shift object
        if (!shift || typeof shift !== 'object' ||
            typeof shift.r !== 'number' || typeof shift.g !== 'number' || typeof shift.b !== 'number' ||
            isNaN(shift.r) || isNaN(shift.g) || isNaN(shift.b)) {
            console.warn('Invalid shift object:', shift);
            return color; // Return original if shift is invalid
        }

        // Apply the shift
        r = Math.max(0, Math.min(255, Math.round(r * shift.r)));
        g = Math.max(0, Math.min(255, Math.round(g * shift.g)));
        b = Math.max(0, Math.min(255, Math.round(b * shift.b)));

        // Final validation check
        if (isNaN(r) || isNaN(g) || isNaN(b)) {
            console.warn('Color calculation resulted in NaN:', { original: color, shift, result: { r, g, b } });
            return color; // Return original if calculation failed
        }

        // Convert back to hex
        return '#' +
            r.toString(16).padStart(2, '0') +
            g.toString(16).padStart(2, '0') +
            b.toString(16).padStart(2, '0');
    }

    /**
 * Generates a random color with fixed brightness in hexadecimal format
 * @param {number} brightness - Value between 0 and 1 (0 = dark, 1 = bright)
 * @returns {string} Hexadecimal color string (e.g. "#FF5A32")
 */
     getRealRandomColor(brightness = 0.7) {
        // Clamp brightness between 0 and 1
        brightness = Math.max(0, Math.min(1, brightness));

        // Convert brightness to HSL lightness value (0-100)
        const lightness = brightness * 100;

        // Generate random hue (0-360)
        const hue = Math.floor(art_Art/* Art */.L.rand() * 360);

        // Generate random saturation (50-100 for more vibrant colors)
        const saturation = 50 + Math.floor(art_Art/* Art */.L.rand() * 50);

        // Convert HSL to RGB
        const h = hue / 360;
        const s = saturation / 100;
        const l = lightness / 100;

        let r, g, b;

        if (s === 0) {
        r = g = b = l; // achromatic
        } else {
        const hue2rgb = (p, q, t) => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        };

        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;

        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
        }

        // Convert RGB to hex
        const toHex = (x) => {
        const hex = Math.round(x * 255).toString(16);
        return hex.length === 1 ? '0' + hex : hex;
        };

        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    }
    addNamedColor(name, color) {
        this.colors[name] = color;
    }

    /*
    addColor(color) {
        this.colors.push(color);
    }

    removeColor(index) {
        if (index < 0 || index >= this.colors.length) {
            throw new Error('Index out of bounds');
        }
        this.colors.splice(index, 1);
    }*/



    getColorDarker(index,type=null, amount) {
        console.log("getColorDarker",index,type,amount)
        if(type==null) type="main"
        let colors=this.colors[type]
        // Validate inputs
        if (index < 0 || index >= colors.length) {
            throw new Error('Index out of bounds');
        }
        if (amount < 0 || amount > 1) {
            throw new Error('Amount must be between 0 and 1');
        }

        let color = colors[index];
        return this.getColorDarkerEx(color, amount);

    }


    getColorDarkerEx(color,amount) {
        // Convert hex to RGB
        let r = parseInt(color.slice(1, 3), 16);
        let g = parseInt(color.slice(3, 5), 16);
        let b = parseInt(color.slice(5, 7), 16);

        // Perceptive luminance weights
        // These weights reflect how human eyes perceive color brightness
        const luminanceWeights = [0.299, 0.587, 0.114];

        // Calculate original luminance
        const originalLuminance =
            r * luminanceWeights[0] +
            g * luminanceWeights[1] +
            b * luminanceWeights[2];

        // Calculate new luminance
        const newLuminance = originalLuminance * (1 - amount);

        // Calculate scaling factor
        const luminanceRatio = newLuminance / originalLuminance;

        // Scale RGB components proportionally
        r = Math.max(0, Math.min(255, Math.round(r * luminanceRatio)));
        g = Math.max(0, Math.min(255, Math.round(g * luminanceRatio)));
        b = Math.max(0, Math.min(255, Math.round(b * luminanceRatio)));

        // Convert back to hex
        return '#' +
            r.toString(16).padStart(2, '0') +
            g.toString(16).padStart(2, '0') +
            b.toString(16).padStart(2, '0');
    }

    getColorLighterEx(color, amount) {
        // Convert hex to RGB
        let r = parseInt(color.slice(1, 3), 16);
        let g = parseInt(color.slice(3, 5), 16);
        let b = parseInt(color.slice(5, 7), 16);

        // Calculate how much room we have to lighten each component
        const headroom = [
            255 - r,
            255 - g,
            255 - b
        ];

        // Apply the lightening amount proportionally to each component
        r = Math.min(255, Math.round(r + headroom[0] * amount));
        g = Math.min(255, Math.round(g + headroom[1] * amount));
        b = Math.min(255, Math.round(b + headroom[2] * amount));

        // Convert back to hex
        return '#' +
            r.toString(16).padStart(2, '0') +
            g.toString(16).padStart(2, '0') +
            b.toString(16).padStart(2, '0');
    }

    //for a generic fillStyle
    static adjustBrightness(color, amount) {
        // Create a temporary canvas to parse the color
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        const ctx = canvas.getContext('2d');

        // Set the fillStyle and fill a pixel to get the computed color
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, 1, 1);

        // Get the pixel data (RGBA values)
        const pixelData = ctx.getImageData(0, 0, 1, 1).data;

        // Extract RGB values
        let r = pixelData[0];
        let g = pixelData[1];
        let b = pixelData[2];
        const a = pixelData[3] / 255; // Convert alpha to 0-1 range

        // Adjust brightness
        if (amount < 0) {
          // Darken
          const factor = 1 + amount;
          r = Math.max(0, Math.round(r * factor));
          g = Math.max(0, Math.round(g * factor));
          b = Math.max(0, Math.round(b * factor));
        } else {
          // Lighten
          const factor = amount;
          r = Math.min(255, Math.round(r + (255 - r) * factor));
          g = Math.min(255, Math.round(g + (255 - g) * factor));
          b = Math.min(255, Math.round(b + (255 - b) * factor));
        }

        // Return the new color as rgba
        return `rgba(${r}, ${g}, ${b}, ${a})`;
      }

      /*
    get length() {
        return this.colors.length;
    }*/
}


 let palette = new Palette({
            "main": [
                '#D4D3C9',
                '#A0B3B7',
                '#40629A',
                '#548093',
                '#FFFFFF' // Added white for snow
            ],
            "landscape2": [
                "#856A7E",
                "#766D75",
                "#A59B90"
            ],
            "landscape2": [
                "#B6A787",
                "#A59B90",

            ],
            "roof": [
               // '#282629',

                '#B5785D',
                '#B6A787',
                '#ffffff',

                '#92ACB4',
                '#C5D4DD',
                '#F2E2CE'
            ],
            "landscape": [
                //'#333333',
                "#FFFFFF", // Added white for snow
                "#F2F2F2", // Added light grey for snow variation
                "#E6E6E6", // Added another light grey for snow variation
                "#CCCCCC", // Added grey for snow variation
                "#E6E6E6", // Added darker grey for snow variation



            ],

            "sky": [
                '#ffffff',
                '#000000',
                '#92ACB4',
                '#C5D4DD',
                '#F2E2CE'
            ],
            "house": [ //param fusta o colors o les dos
            /*    '#8B4513',  // Saddle Brown
                '#CD853F',  // Peru
                '#D2691E',  // Chocolate
                '#A0522D',  // Sienna
                '#DEB887',  // Burlywood
                '#F4A460',  // Sandy Brown
                '#D2B48C',  // Tan
                '#FFDAB9', // Peach Puff
                */

                '#0A5C2A',  // More saturated dark green
                '#FFD700',  // More saturated gold
                '#FF4500',  // More saturated red-orange
                '#1E90FF',  // More saturated blue
                '#E0E0E0',   // Kept light grey as is for contrast
                '#4A6D7C',   // Nordic blue-grey
                '#8A9A5B'    // Nordic sage green
            ]
        });



       art_Art/* Art */.L.palette=palette
;// CONCATENATED MODULE: ./src/art/Scene.js


class Scene {
    constructor() {
        this.layers = [{ zIndex: 0, shapes: [] }];
        this.layerBitmaps = {};
        this.isLayerBitmapEnabled = false;
        this.count=0
    }

    addLayer(inlayer, zIndex) {
        // Ensure the inlayer has a margin property (default to 0 if not set)
        if (inlayer.margin === undefined) {
            inlayer.margin = 0;
        }

        // Add the layer to the scene
        this.add(inlayer, zIndex);

        // Find the layer we just added and set its margin
        let layer = this.layers.find(layer => layer.zIndex === zIndex);
        if (layer) {
            layer.margin = inlayer.margin;
            console.log(`Added layer with z-index ${zIndex} and margin ${layer.margin}`);
        }

        return this;
    }

    add(shape, zIndex = 0) {
        let layer = this.layers.find(layer => layer.zIndex === zIndex);
        if (!layer) {
            layer = { zIndex, shapes: [] };
            this.layers.push(layer);
            this.layers.sort((a, b) => a.zIndex - b.zIndex);
        }
        layer.shapes.push(shape);

        // Invalidate the layer bitmap if it exists
        if (this.layerBitmaps[zIndex]) {
            this.layerBitmaps[zIndex].isValid = false;
        }

        return this;
    }

    clear() {
        art_Art/* Art */.L.clearRect(0, 0, art_Art/* Art */.L.ctx.canvas.width, art_Art/* Art */.L.ctx.canvas.height);
        return this;
    }

    /**
     * Creates or updates bitmaps for all layers or a specific layer
     * @param {number|null} zIndex - Optional zIndex to update only one layer
     * @returns {Scene} - Returns this for chaining
     */

    allPainted(){
        const maxShapes = Math.max(...this.layers.map(layer => layer.shapes.length));

        return this.count >= maxShapes;
    }
    createLayerBitmaps(zIndex = null) {
        // Enable layer bitmap mode
        this.isLayerBitmapEnabled = true;

        const layersToProcess = zIndex !== null
            ? this.layers.filter(layer => layer.zIndex === zIndex)
            : this.layers;

            this.count+=32 //velocitat

        for (const layer of layersToProcess) {


     
            // Create a canvas for this layer
            const canvas = document.createElement('canvas');

            // Ensure layer.margin is defined and valid
            const margin = layer.margin !== undefined ? layer.margin : 0;

            // Ensure we create a canvas with valid dimensions (at least 1x1)
            const canvasWidth = Math.max(1, art_Art/* Art */.L.width * (1 + margin) * art_Art/* Art */.L.ratio);
            const canvasHeight = Math.max(1, art_Art/* Art */.L.height * (1 + margin) * art_Art/* Art */.L.ratio);

            canvas.width = canvasWidth;
            canvas.height = canvasHeight;

       
            const ctx = canvas.getContext('2d', { alpha: true });


            // Clear the canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Save original context and swap
            const originalCtx = art_Art/* Art */.L.ctx;
            art_Art/* Art */.L.ctx = ctx;

            // Calculate offset to center content (account for the margin)
            // Use the margin variable we defined earlier
            const marginOffset = art_Art/* Art */.L.width * margin * art_Art/* Art */.L.ratio / 2;
            const marginOffsetY = art_Art/* Art */.L.height * margin * art_Art/* Art */.L.ratio / 2;

            // Save context state before translation
            art_Art/* Art */.L.ctx.save();

            // Translate to account for margin (position shapes within the expanded canvas)
            // Center the content in the middle of the expanded canvas
            art_Art/* Art */.L.ctx.translate(marginOffset, marginOffsetY);

            // Paint all shapes in this layer to the canvas
       
            let i=0
            for (const shape of layer.shapes) {
                // Check if the shape has a paint method before calling it
                if (typeof shape.paint === 'function') {
                    shape.paint();
                } else {
                    console.warn('Shape does not have a paint method:', shape);
                }
                i++
                if(i>this.count) break;
            }

            // Restore context state (remove translation)
            art_Art/* Art */.L.ctx.restore();

            // Apply fog effect if enabled and this is a distant layer
            this.applyFogEffect(ctx, layer.zIndex, canvasWidth, canvasHeight);

            // Restore original context
            art_Art/* Art */.L.ctx = originalCtx;

            // Create and store bitmap info
            this.layerBitmaps[layer.zIndex] = {
                canvas: canvas,
                ctx: ctx,
                zIndex: layer.zIndex,
                isValid: true
            };
        }

        return this;
    }

    /**
     * Apply fog effect to distant layers when fog parameter is enabled
     * @param {CanvasRenderingContext2D} ctx - The canvas context to apply fog to
     * @param {number} zIndex - The zIndex of the current layer
     * @param {number} canvasWidth - Width of the canvas
     * @param {number} canvasHeight - Height of the canvas
     */
    applyFogEffect(ctx, zIndex, canvasWidth, canvasHeight) {
        // Check if fog is enabled
        let fogEnabled = false;
        try {
            fogEnabled = typeof $fx !== 'undefined' ? $fx.getParam("fog") : false;
        } catch (e) {
            // If $fx is not available or parameter is not defined, fog is disabled
            fogEnabled = false;
        }

        if (!fogEnabled) {
            return; // Fog is disabled, do nothing
        }

        // Determine if this layer should have fog applied
        // Apply fog to distant layers, excluding the 2 closest ones
        // Based on the layer structure: sun layer (-1), landscape layers (0-5)
        // We want to exclude the 2 closest landscape layers (4 and 5)
        const shouldApplyFog = zIndex <= 3; // Apply to sun layer (-1) and landscape layers 0, 1, 2, 3

        if (!shouldApplyFog) {
            return; // This layer doesn't need fog
        }

        // Save the current context state
        ctx.save();

        // Set the fog properties - simple white overlay with 0.1 opacity
        ctx.globalCompositeOperation = 'source-over';
        ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';

        // Fill the entire canvas with the fog layer
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // Restore the context state
        ctx.restore();
    }

    /**
     * Paints a specific layer bitmap at the given position
     * @param {number} zIndex - The zIndex of the layer to paint
     * @param {number} x - X position
     * @param {number} y - Y position
     * @returns {Scene} - Returns this for chaining
     */
    paintLayerBitmap(zIndex, x = 0, y = 0) {
        // Handle case where layerBitmaps might not be initialized
        if (!this.layerBitmaps) {
            console.warn('Layer bitmaps not initialized');
            return this;
        }

        const bitmap = this.layerBitmaps[zIndex];
        const layer = this.layers.find(layer => layer.zIndex === zIndex);

        if (!bitmap || !bitmap.isValid) {
           // this.createLayerBitmaps(zIndex);
        }

        if (this.layerBitmaps[zIndex]) {
            const bitmap = this.layerBitmaps[zIndex];
            const canvas = bitmap.canvas;

            // Check if the canvas has valid dimensions before drawing
            if (canvas.width <= 0 || canvas.height <= 0) {
                console.warn(`Cannot draw layer ${zIndex}: Canvas has invalid dimensions (${canvas.width}x${canvas.height})`);
                return this;
            }

            // Calculate margin offset to position the layer correctly
            const margin = layer && layer.margin ? layer.margin : 0;

            // Position the layer bitmap so that the center of the actual content
            // aligns with the center of the canvas view
            const marginOffsetX = margin * art_Art/* Art */.L.width * art_Art/* Art */.L.ratio / 2;
            const marginOffsetY = margin * art_Art/* Art */.L.height * art_Art/* Art */.L.ratio / 2;

            // The x,y parameters represent the parallax offset from center
            try {
                art_Art/* Art */.L.ctx.drawImage(
                    canvas,
                    x - marginOffsetX,
                    y - marginOffsetY
                );
            } catch (error) {
                console.error(`Error drawing layer ${zIndex}:`, error);
            }
        }

        return this;
    }

    /**
     * Paint all layer bitmaps in order
     * @returns {Scene} - Returns this for chaining
     */
    paintAllLayerBitmaps(x = 0, y = 0) {
        // Handle case where layerBitmaps might not be initialized
        if (!this.layerBitmaps) {
            console.warn('Layer bitmaps not initialized');
            return this;
        }

        // Sort layers by zIndex
        const layerIndices = Object.keys(this.layerBitmaps)
            .map(Number)
            .sort((a, b) => a - b); // This will sort negative indices first

        // Paint each layer bitmap
        for (const zIndex of layerIndices) {
            // Each layer will be positioned using its own margin offset in paintLayerBitmap
            this.paintLayerBitmap(zIndex, x, y);
        }

        return this;
    }

    /**
     * Enables or disables the use of layer bitmaps
     * @param {boolean} enabled - Whether to enable layer bitmaps
     * @returns {Scene} - Returns this for chaining
     */
    useLayerBitmaps(enabled = true) {
        this.isLayerBitmapEnabled = enabled;
        return this;
    }

    paint(zIndex = null) {
        if (this.isLayerBitmapEnabled) {
            if (zIndex !== null) {
                this.paintLayerBitmap(zIndex);
            } else {
                this.paintAllLayerBitmaps();
            }
            return this;
        }

        // Original painting logic when not using bitmaps
        if (zIndex !== null) {
            const layer = this.layers.find(layer => layer.zIndex === zIndex);
            if (layer) {
                layer.shapes.forEach(shape => {
                    if (typeof shape.paint === 'function') {
                        shape.paint(art_Art/* Art */.L.ctx);
                    } else {
                        console.warn('Shape does not have a paint method:', shape);
                    }
                });
            }
        } else {
            this.layers.forEach(layer => {
                layer.shapes.forEach(shape => {
                    if (typeof shape.paint === 'function') {
                        shape.paint(art_Art/* Art */.L.ctx);
                    } else {
                        console.warn('Shape does not have a paint method:', shape);
                    }
                });
            });
        }
        return this;
    }

    debug() {
        /*
        this.layers.forEach(layer => {
            layer.debug();
        });
        return this;*/
    }
}
// EXTERNAL MODULE: ./src/art/Style.js
var Style = __webpack_require__(222);
;// CONCATENATED MODULE: ./src/art/ContourLayer.js
// Class for handling landscape layers with contour lines





class ContourLayer extends art_Drawable/* Drawable */.Q {
    constructor(points = []) {
        super();

        // Create an internal polygon for containment checks and other operations
        this.polygon = new art_Polygon.Polygon(points);

        // Copy points from the polygon
        this.points = this.polygon.points;

        // Default properties
        this.grid_dist = art_Art/* Art */.L.grid_dist;
        this.margin = 0;
        this.spots = [];
        this.contourSpacing = 20; // Default spacing between contour lines (in pixels)
    }

    // Delegate containsPoint to the internal polygon
    containsPoint(point) {
        return this.polygon.containsPoint(point);
    }

    // Delegate width calculation to the internal polygon
    width() {
        return this.polygon.width();
    }

    // Delegate height calculation to the internal polygon
    height() {
        return this.polygon.height();
    }

    // Delegate getYAtX to the internal polygon
    getYAtX(x) {
        return this.polygon.getYAtX(x);
    }

    // Add a point to the layer
    addPoint(point) {
        this.points.push(point);
        this.polygon.addPoint(point);
        return this;
    }

    // Set the style for the layer
    setStyle(style) {
        this.style = style;
        this.polygon.setStyle(style);
        return this;
    }

    // Paint the layer with parallel contour lines
    paint() {
        // Apply the style
        this.style.apply();

        // First, fill the polygon with the base color
        this.polygon.fill();

        // Then draw the contour lines
        //this.drawContourLines();

        // Finally, draw the outline
        this.polygon.stroke();
    }

    // Draw parallel contour lines along the edges of the polygon
    drawContourLines() {
        // Save the current context state
        art_Art/* Art */.L.ctx.save();

        // Set the style for contour lines
        art_Art/* Art */.L.ctx.strokeStyle = "#000000";
        // Make line width proportional to grid_dist for better scaling with perspective
        // Increase the multiplier to make lines more visible in nearer layers
        art_Art/* Art */.L.ctx.lineWidth = (this.grid_dist * 0.01) * art_Art/* Art */.L.ratio;

        // Find the top edge of the landscape (the mountain profile)
        const topEdge = this.findTopEdge();

        // Draw contour lines parallel to the top edge
        this.drawParallelContours(topEdge);

        // Restore the context state
        art_Art/* Art */.L.ctx.restore();
    }

    // Find the top edge of the landscape (the mountain profile)
    findTopEdge() {
        const points = this.points;
        const topEdge = [];

        // Find the leftmost and rightmost x-coordinates
        const xValues = points.map(p => p.x);
        const minX = Math.min(...xValues);
        const maxX = Math.max(...xValues);

        // Find the top points that form the mountain profile
        // We'll look for points where y is decreasing (going up) then increasing (going down)
        let topPoints = [];

        // First, find all points that could be part of the top edge
        for (let i = 0; i < points.length; i++) {
            const p = points[i];
            const nextP = points[(i + 1) % points.length];

            // If this segment is mostly horizontal and near the top of the polygon,
            // it's likely part of the top edge
            if (Math.abs(nextP.y - p.y) < Math.abs(nextP.x - p.x) * 0.5) {
                // Check if this point is in the top half of the polygon
                const bounds = this.getBounds();
                const midY = (bounds.minY + bounds.maxY) / 2;

                if (p.y < midY && nextP.y < midY) {
                    topPoints.push(p);
                    // Don't add nextP here to avoid duplicates
                }
            }
        }

        // Sort points by x-coordinate
        topPoints.sort((a, b) => a.x - b.x);

        // Always use the sampling approach to ensure consistent coverage
        // Use more segments for distant mountains (smaller grid_dist)
        const segmentCount = Math.max(40, Math.floor(100 / (this.grid_dist / 50)));
        const step = (maxX - minX) / segmentCount;

        // Ensure we start slightly before the left edge and end slightly after the right edge
        // This helps with coverage on the edges
        const startX = minX - step;
        const endX = maxX + step;

        for (let x = startX; x <= endX; x += step) {
            let minY = Infinity;
            let topPoint = null;

            // Find the highest point (minimum y) at this x-coordinate
            for (let i = 0; i < points.length; i++) {
                const p1 = points[i];
                const p2 = points[(i + 1) % points.length];

                // Skip vertical edges
                if (p1.x === p2.x) continue;

                // Check if x is within this edge
                if ((x >= p1.x && x <= p2.x) || (x >= p2.x && x <= p1.x)) {
                    // Calculate the y-coordinate at this x
                    const t = (x - p1.x) / (p2.x - p1.x);
                    const y = p1.y + t * (p2.y - p1.y);

                    if (y < minY) {
                        minY = y;
                        topPoint = new art_Point/* Point */.E(x, y);
                    }
                }
            }

            if (topPoint) {
                topEdge.push(topPoint);
            }
        }

        // If we still don't have enough points, add the original topPoints as a fallback
        if (topEdge.length < 2 && topPoints.length >= 2) {
            topEdge.push(...topPoints);
        }

        return topEdge;
    }

    // Draw contour lines parallel to the top edge
    drawParallelContours(topEdge) {
        if (topEdge.length < 2) return; // Need at least 2 points to draw lines

        // Get the bounds of the polygon
        const bounds = this.getBounds();

        // Use $fx parameter to control contour density if available
        let densityFactor = 1.0;
        try {
            densityFactor = $fx.getParam("contour_density");
        } catch (e) {
            // If $fx is not available or parameter is not defined, use default
            console.log("Using default contour density");
        }

        // Define minimum spacing between contour lines (in pixels)
        const minSpacingPx = 5;

        // Convert to logical units based on Art.ratio
        const minSpacing = minSpacingPx / art_Art/* Art */.L.ratio;

        // Apply density factor to contour spacing (smaller spacing = more lines)
        const adjustedSpacing = this.contourSpacing / densityFactor;

        // Calculate actual spacing to use (max of minSpacing and adjustedSpacing)
        const actualSpacing = Math.max(adjustedSpacing, minSpacing);

        // Recalculate number of contours based on actual spacing
        const actualMaxContours = Math.floor((bounds.maxY - bounds.minY) / actualSpacing);

        // Draw each contour line
        for (let i = 1; i <= actualMaxContours; i++) {
            const offset = i * actualSpacing;

            // Create a path for this contour
            art_Art/* Art */.L.ctx.beginPath();

            // Start at the first point
            let started = false;
            let lastValidX = null;
            let lastValidY = null;
            let segmentPoints = [];

            // First pass: collect all valid points for this contour
            for (let j = 0; j < topEdge.length; j++) {
                const p = topEdge[j];
                const x = p.x;
                const y = p.y + offset; // Offset downward

                // Skip if the point is outside the polygon's vertical bounds
                if (y > bounds.maxY) continue;

                // Check if this point is inside the polygon
                const point = new art_Point/* Point */.E(x, y);
                if (this.containsPoint(point)) {
                    segmentPoints.push(point);
                }
            }

            // Second pass: identify and draw continuous segments
            if (segmentPoints.length > 0) {
                // Sort points by x-coordinate to ensure proper ordering
                segmentPoints.sort((a, b) => a.x - b.x);

                // Find continuous segments
                let currentSegment = [segmentPoints[0]];

                for (let j = 1; j < segmentPoints.length; j++) {
                    const currentPoint = segmentPoints[j];
                    const prevPoint = segmentPoints[j - 1];

                    // Check if this point is part of the current segment
                    // Use a threshold based on grid_dist to determine continuity
                    // Increase the threshold for better connectivity in all layers
                    const threshold = this.grid_dist * 1.0;
                    if (currentPoint.x - prevPoint.x <= threshold) {
                        // Points are close enough, add to current segment
                        currentSegment.push(currentPoint);
                    } else {
                        // Gap detected, draw the current segment and start a new one
                        if (currentSegment.length > 1) {
                            // Draw this segment
                            art_Art/* Art */.L.ctx.beginPath();
                            art_Art/* Art */.L.moveTo(currentSegment[0].x * art_Art/* Art */.L.ratio, currentSegment[0].y * art_Art/* Art */.L.ratio);

                            for (let k = 1; k < currentSegment.length; k++) {
                                art_Art/* Art */.L.lineTo(currentSegment[k].x * art_Art/* Art */.L.ratio, currentSegment[k].y * art_Art/* Art */.L.ratio);
                            }

                            art_Art/* Art */.L.ctx.stroke();
                        }

                        // Start a new segment
                        currentSegment = [currentPoint];
                    }
                }

                // Draw the last segment
                if (currentSegment.length > 1) {
                    art_Art/* Art */.L.ctx.beginPath();
                    art_Art/* Art */.L.moveTo(currentSegment[0].x * art_Art/* Art */.L.ratio, currentSegment[0].y * art_Art/* Art */.L.ratio);

                    for (let k = 1; k < currentSegment.length; k++) {
                        art_Art/* Art */.L.lineTo(currentSegment[k].x * art_Art/* Art */.L.ratio, currentSegment[k].y * art_Art/* Art */.L.ratio);
                    }

                    art_Art/* Art */.L.ctx.stroke();
                }
            }
        }
    }

    // Get the bounds of the polygon
    getBounds() {
        return this.polygon.getBounds();
    }

    // Stroke the outline of the polygon
    stroke() {
        this.polygon.stroke();
        return this;
    }

    // Fill the polygon
    fill() {
        this.polygon.fill();
        return this;
    }

    // Trace the polygon path without filling or stroking
    trace() {
        this.polygon.trace();
        return this;
    }
}

;// CONCATENATED MODULE: ./src/art/Landscape.js










class Landscape{
    // dist=1200/16 //todo config relacionat amb canvas?
    generated=false
    layers = [];
   // dirs=[Art.RIGHT,Art.RIGHT-Art.TAU/8,Art.RIGHT+Art.TAU/8,Art.RIGHT-Art.TAU/16,Art.RIGHT+Art.TAU/16]  //param
    //dirs=[Art.RIGHT,Art.RIGHT-Art.TAU/8,Art.RIGHT+Art.TAU/8]  //param
    dirs=[art_Art/* Art */.L.RIGHT,art_Art/* Art */.L.RIGHT-art_Art/* Art */.L.TAU/32,art_Art/* Art */.L.RIGHT+art_Art/* Art */.L.TAU/32]

    constructor(canvas) {

        this.canvas = canvas;
        this.width = canvas.width;
        this.height = canvas.height;
    }

    createLayers() {
        // Create 4 layers, each with different height positions, grid distances, and margins
        // Parameters: (height ratio, grid distance, margin ratio)
        // Height ratio: position on the canvas (0-1)
        // Grid distance: spacing between grid points for objects
        // Margin ratio: how much the layer extends beyond canvas boundaries

        // Layers are added in back-to-front order (further layers first)
       // this.createLayer(0.1, 120, 0.5)
       let step=Tools.randInt(5)*0.1
        this.createLayer(0.4-step, 100, 0.4)
        this.createLayer(0.5-step, 87.5, 0.3)
        this.createLayer(0.6-step, 75, 0.35)
       this.createLayer(0.7-step, 62.5, 0.2)
        this.createLayer(0.8-step, 50, 0.15)
        this.createLayer(0.9-step, 37.5, 0.1)


        // Generate grid points inside each layer's polygon
        this.generateSpots()

        // Remove points that would be hidden by layers in front
        this.removeInvisible()
    }

    //between 0 and 1
    createLayer(height,grid_dist,margin) {

        const slope=$fx.getParam("mountain_slope");
        this.dirs=[
            art_Art/* Art */.L.RIGHT,
            art_Art/* Art */.L.RIGHT-(art_Art/* Art */.L.TAU/slope)*this.layers.length,
            art_Art/* Art */.L.RIGHT+(art_Art/* Art */.L.TAU/slope)*this.layers.length
        ]

      /*  if(this.layers.length==5){
            this.dirs=[
                Art.RIGHT,
                Art.RIGHT-(Art.TAU/8),
                Art.RIGHT+(Art.TAU/8)
            ]
        }*/
        let that = this;
        // Apply margin to bottom and top
        const bottomY = this.height + art_Art/* Art */.L.height * margin;
        const layerY = bottomY - (height * this.height);

        // Starting point with margin on the left
        const p0 = new art_Point/* Point */.E(0 - art_Art/* Art */.L.width * margin, layerY);

        let currentDir = Tools.getRandFromArray(this.dirs);
        let dir = currentDir;
        let dist_travelled = 0;
        let inix = p0.x; // Use p0.x instead of hardcoded value
        let indx = 0;

        let poly = Path/* Path */.y.getPolarLineCondition(
            p0,
            function(p) {
                return dir;
            },
            function(p) {
                dist_travelled += p.x - inix;

                // Change target direction every 400 units instead of 300
                if (dist_travelled > 200) { //param
                    currentDir = Tools.getRandFromArray(that.dirs);
                    dist_travelled = 0;
                }

                inix = p.x;

                // Calculate shortest angular difference
                let delta = currentDir - dir;
                delta = delta - 360 * Math.round(delta / 360);

                // Smooth interpolation with angular awareness
                dir += delta * 0.1; // param

                // Apply margin to the right boundary (consistent margin usage)
                return p.x < art_Art/* Art */.L.width + art_Art/* Art */.L.width * margin;
            }
        );
        // Define a simple fallback for getTangentAngle if needed
        if (!poly.getTangentAngle) {
            console.warn("Adding getTangentAngle method to poly");
            poly.getTangentAngle = function(indx) {
                let p1, p2;
                if (indx === 0) {
                    p1 = this.points[indx];
                    p2 = this.points[indx + 1];
                } else if (indx === this.points.length - 1) {
                    p1 = this.points[indx - 1];
                    p2 = this.points[indx];
                } else {
                    p1 = this.points[indx - 1];
                    p2 = this.points[indx + 1];
                }
                return Math.atan2(p2.y - p1.y, p2.x - p1.x);
            };
        }

        // Set angle and index for each point
        poly.points.forEach((p, indx) => {
            p.angle = poly.getTangentAngle(indx) + art_Art/* Art */.L.TAU * 0.4;
            p.indx = indx;
        });

        // Close the polygon with points that form a complete closed shape
        // Right bottom corner with margin
        const rightX = art_Art/* Art */.L.width + art_Art/* Art */.L.width * margin;
        poly.addPoint(new art_Point/* Point */.E(rightX, bottomY));

        // Left bottom corner with margin
        const leftX = 0 - art_Art/* Art */.L.width * margin;
        poly.addPoint(new art_Point/* Point */.E(leftX, bottomY));

        // Back to starting point
        poly.addPoint(p0);

        // Create a ContourLayer instead of a Polygon
        const layer = new ContourLayer(poly.points);
        layer.grid_dist = grid_dist*0.5; //param
        layer.margin = margin;

        // Set the contour spacing proportional to the grid distance
        // This ensures the contour lines scale with the perspective of each layer
        // For distant mountains (small grid_dist), use a larger percentage to avoid too many lines
        const minSpacingPx = 5 / art_Art/* Art */.L.ratio; // Minimum spacing in logical units
        const proportionalSpacing = grid_dist * (grid_dist < 50 ? 0.3 : 0.2); // Adjust percentage based on distance
        layer.contourSpacing = Math.max(proportionalSpacing, minSpacingPx); // Use the larger value

        // Set a default style for the layer
        layer.setStyle({
            fillStyle: art_Art/* Art */.L.palette.getRandColor("landscape"),
            strokeStyle: "#000000",
            lineWidth: grid_dist * 0.01 * art_Art/* Art */.L.ratio
        });

        this.layers.unshift(layer);
    }



    generateSpots(){
        this.layers.forEach((layer, index) => {

            console.log("layer", layer.dist);
            layer.spots = this.generateGrid(layer,  layer.grid_dist);
            layer.spots.sort((a, b) => {
                if (a.y === b.y) {
                    return a.x - b.x;
                }
                return a.y - b.y;
            });
            layer.spots.forEach((spot, indx) => {
                spot.indx = indx;
            });
        });
        this.generated=true
    }

    generateGrid(layer, d) {
        let points = [];
        const margin = layer.margin || 0;

        // Start from negative margins and extend beyond canvas width/height based on margin
        const startX = 0 - art_Art/* Art */.L.width * margin;
        const startY = 0 - art_Art/* Art */.L.height * margin;
        const endX = art_Art/* Art */.L.width + art_Art/* Art */.L.width * margin;
        const endY = art_Art/* Art */.L.height + art_Art/* Art */.L.height * margin;

        // Generate grid points including margin areas (use layer's grid_dist for spacing)
        for (let x = startX; x <= endX; x += d) {
            for (let y = startY; y <= endY; y += d) {
                const point = new art_Point/* Point */.E(x, y);

                // Check if the point is inside the layer's polygon shape
                if (layer.containsPoint(point)) {
                    points.push(point);
                }
            }
        }

        // Debug log
        console.log(`Generated ${points.length} grid points for layer with margin ${margin}`);

        return points;
    }

    registerDrawables(layer_index,callback){
        let layer = this.layers[layer_index];
        // Use the layer-specific grid distance for this layer
        const layerGridDist = layer.grid_dist || art_Art/* Art */.L.grid_dist;

        layer.spots.forEach(point=>{
            //search how many next points there are
            let nextPoints = [];
            let c=1
            for(let i=point.indx+1;i<layer.spots.length;i++){
                if(layer.spots[i].x==point.x+layerGridDist*c && layer.spots[i].y==point.y){
                    nextPoints.push(layer.spots[i]);
                }else{
                    break;
                }
                c++
            }
            point.nextPoints = nextPoints;
            callback(point)
        })
    }



        //todo no pot estar aqui
    isPointVisible(point, index) {
        // Check if this point (in the current layer) is covered by any higher layer

        // For each higher layer (with larger index)
        for (let i = index+1; i < this.layers.length; i++) {
            let otherLayer = this.layers[i];
            // Check the actual point without any adjustment
            if (otherLayer.containsPoint(point)) {
                return false; // Point is covered by a higher layer
            }
        }
        return true; // Point is not covered by any higher layer
    }

    removeInvisible(){


        this.layers.forEach((layer, index) => {
            layer.spots.forEach(point => {
                let visible = this.isPointVisible(point, index);
                if(visible) {
                    point.visible = true;
                }else{
                    point.visible = false;
                }

          });
        });

    }


    debug() {
        if(!this.generated){
            throw("You must generate the spots first");
            return;
        }
        this.layers.forEach((layer, index) => {
            if(index==0){
            let col = 'rgba(255,255,255,0.5)';

            layer.style.fillStyle = col;

            //layer.fill();
            layer.stroke();

            console.log("index", index);
            //nomes debug
            layer.spots.forEach(point => {
                let visible = this.isPointVisible(point, index);
                if(visible) {
                    art_Art/* Art */.L.ctx.fillStyle = 'red';
                    art_Art/* Art */.L.ctx.fillRect
                    (point.x*art_Art/* Art */.L.ratio+index,
                        point.y*art_Art/* Art */.L.ratio,
                        5*art_Art/* Art */.L.ratio,
                        5*art_Art/* Art */.L.ratio);
                        point.visible = true
                } else {
                    art_Art/* Art */.L.ctx.fillStyle = 'blue';
                    art_Art/* Art */.L.ctx.fillRect(point.x*art_Art/* Art */.L.ratio+index*14, point.y*art_Art/* Art */.L.ratio, 8*art_Art/* Art */.L.ratio, 8*art_Art/* Art */.L.ratio);
                    point.visible = false;
                }
               // point.debug();
            });
        }

        });
    }
}

class gridPoint {
    constructor(x, y, n, m) {
        this.x = x;
        this.y = y;
    }
}
;// CONCATENATED MODULE: ./src/art/LandscapeForms.js




class LandscapeGround extends (/* unused pure expression or super */ null && (Drawable)){
    constructor(point, gridd_w) {
        super();
        this.point=point
        this.gridd_w=gridd_w
       
    }

    paint(){    
        this.setStyle({
            lineWidth: 0.15,
            strokeStyle: Art.palette.getColor("landscape"),
           

        })
       //this.style.apply();
        Art.ctx.beginPath()
        Art.moveTo(this.point.x, this.point.y)
        Art.lineTo(
            this.point.x + this.gridd_w * Art.grid_dist, 
            this.point.y)
       // Art.lineTo(this.point.x + this.gridd_w*2, this.point.y)
        Art.ctx.stroke()
       
    }
}


class LandscapeSnow extends art_Drawable/* Drawable */.Q{
    constructor(point, gridd_w) {
        super();
        this.point = point;
        this.point=this.point.move(-art_Art/* Art */.L.grid_dist*0.5,0)
        this.gridd_w = gridd_w; //todo corregir
        // Random height for the snow pile - between 10% and 30% of grid distance
        
        // Store the current grid_dist at construction time to ensure perspective scaling
        this.currentGridDist = art_Art/* Art */.L.grid_dist;
        
        //param fins 4
        this.snowHeight =  0.25 * this.currentGridDist + 0.1 * this.currentGridDist;
        // Random width factor to vary the snow pile width
        this.widthFactor = 0.5 +  0.5; // Between 50-100% of grid width
    }

    paint() {
        this.setStyle({
            lineWidth: 1,
            strokeStyle:  "#000",
            fillStyle:  "#fff"
        });
        
        const startX = this.point.x;
        const endX = this.point.x + (this.gridd_w * this.currentGridDist * this.widthFactor);
        const midX = (startX + endX) / 2;
        const baseY = this.point.y;
        const peakY = baseY - this.snowHeight;
        
        // Start drawing the snow pile
        art_Art/* Art */.L.ctx.beginPath();
        
        // Start at the left base point
        art_Art/* Art */.L.moveTo(startX, baseY);
        
        // Create the curved top of the snow pile using quadratic curves
        // Left side curve
        art_Art/* Art */.L.ctx.quadraticCurveTo(
            ((startX + midX) / 2) * art_Art/* Art */.L.ratio, 
            (baseY - this.snowHeight * 0.8) * art_Art/* Art */.L.ratio, 
            midX * art_Art/* Art */.L.ratio, 
            peakY * art_Art/* Art */.L.ratio
        );
        
        // Right side curve
        art_Art/* Art */.L.ctx.quadraticCurveTo(
            ((midX + endX) / 2) * art_Art/* Art */.L.ratio, 
            (baseY - this.snowHeight * 0.8) * art_Art/* Art */.L.ratio, 
            endX * art_Art/* Art */.L.ratio, 
            baseY * art_Art/* Art */.L.ratio
        );
        
        // Close the path to create a complete shape
        art_Art/* Art */.L.ctx.closePath();
        
        // Fill and stroke the snow pile
        art_Art/* Art */.L.ctx.fill();
        art_Art/* Art */.L.ctx.stroke();
        
        // Add some details to make it look more like snow
        this.addSnowDetails(startX, endX, baseY, peakY);
    }
    
    addSnowDetails(startX, endX, baseY, peakY) {
        // Add some small curves or dots to give texture to the snow
        // Only if the snow pile is large enough
        if (this.snowHeight > 0.15 * this.currentGridDist) {
            const width = endX - startX;
            
            // Set a lighter stroke for details
            art_Art/* Art */.L.ctx.strokeStyle = art_Art/* Art */.L.palette.getColor("snow_detail") || "rgba(220, 220, 220, 0.5)";
            art_Art/* Art */.L.ctx.lineWidth = 0.2;
            
            // Add a few subtle curves on the surface
            const detailCount = Math.floor(2 + art_Art/* Art */.L.rand() * 3); // 2-4 details
            
            for (let i = 0; i < detailCount; i++) {
                const detailX = startX + (width * (i + 1)) / (detailCount + 1);
                const detailY = baseY - this.snowHeight * (0.4 + art_Art/* Art */.L.rand() * 0.4);
                
                art_Art/* Art */.L.ctx.beginPath();
                art_Art/* Art */.L.moveTo(detailX - width * 0.05, detailY + this.snowHeight * 0.1);
                art_Art/* Art */.L.ctx.quadraticCurveTo(
                    detailX*art_Art/* Art */.L.ratio, 
                    (detailY - this.snowHeight * 0.05)**art_Art/* Art */.L.ratio, 
                    (detailX + width * 0.05)**art_Art/* Art */.L.ratio, 
                    (detailY + this.snowHeight * 0.1)*art_Art/* Art */.L.ratio
                );
                art_Art/* Art */.L.ctx.stroke();
            }
        }
    }
}


;// CONCATENATED MODULE: ./src/art/Config.js
class Config {
    constructor() {
        this.data = {};
        this.isLoaded = false;
    }

    /**
     * Load configuration from a JSON file
     * @param {string} path - Path to the JSON file
     * @returns {Promise<Config>} - Returns this instance for chaining
     */
    async load(path) {
        try {
            const response = await fetch(path);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.data = await response.json();
            this.isLoaded = true;
            console.log('Config loaded successfully', this.data);
        } catch (error) {
            console.error(`Error loading config} path `+path);
        }

        return this;
    }

    get(key,def) {
        if(this.data[key]==undefined) return def;
        return this.data[key];
    }

    // ... rest of your methods remain the same
}
;// CONCATENATED MODULE: ./src/art/Tracking.js
 

class Tracking{
    mouseX = 0;
    mouseY=0;
    normalizedMouseX = 0;
    normalizedMouseY = 0;
    constructor(canvas,mode="mouse"){
        this.mode=mode;

        if(this.mode=="mouse"){
            // Track mouse position for parallax effect
            document.addEventListener('mousemove', (e) => {
                // Convert mouse position to canvas coordinates
                const rect = canvas.getBoundingClientRect();
                this.mouseX = ((e.clientX - rect.left) / rect.width) * art_Art/* Art */.L.width;
                this.mouseY = ((e.clientY - rect.top) / rect.height) * art_Art/* Art */.L.height;
                this.normalize();
            });
        }
        if(this.mode=="camera"){
            const serverIP = 'localhost'; // TODO: Make this configurable
            const serverPort = 8000;

            const updatePosition = () => {
                fetch(`http://${serverIP}:${serverPort}/position`)
                    .then(response => response.json())
                    .then(data => {
                        console.log(`Position: X=${data.x}, Y=${data.y}, People=${data.num_people}`);
                        this.normalizedMouseX += (data.x- this.normalizedMouseX)/4;
                        this.normalizedMouseY += (data.y- this.normalizedMouseY)/4;
                    })
                    .catch(error => console.error('Error fetching position data:', error));
            };

            // Update position every 100ms
            setInterval(updatePosition, 100);
        }
        if(this.mode=="test"){
            this.normalizedMouseX=0.5
            this.normalizedMouseY=0.5
            this.speedx=art_Art/* Art */.L.rand()*0.1-0.05
            this.speedy=art_Art/* Art */.L.rand()*0.1-0.05
            setInterval(() => {
                console.log("hehe", this.normalizedMouseX)
                this.normalizedMouseX+=this.speedx;
                this.normalizedMouseY+=this.speedy;
                if(this.normalizedMouseX>1 || this.normalizedMouseX<-1){
                    this.speedx*=-1
                }
                if(this.normalizedMouseY>1 || this.normalizedMouseY<-1){
                    this.speedy*=-1
                }
            }, 50);
            
         }
    }
    debug() {
        const crossSize = 0.1; // Normalized size
        const centerX = (this.normalizedMouseX + 1) / 2 * art_Art/* Art */.L.width;
        const centerY = (this.normalizedMouseY + 1) / 2 * art_Art/* Art */.L.height;
        art_Art/* Art */.L.ctx.beginPath();
        art_Art/* Art */.L.moveTo(centerX - crossSize * art_Art/* Art */.L.width, centerY);
        art_Art/* Art */.L.lineTo(centerX + crossSize * art_Art/* Art */.L.width, centerY);
        art_Art/* Art */.L.moveTo(centerX, centerY - crossSize * art_Art/* Art */.L.height);
        art_Art/* Art */.L.lineTo(centerX, centerY + crossSize * art_Art/* Art */.L.height);
        art_Art/* Art */.L.ctx.strokeStyle = 'red';
        art_Art/* Art */.L.ctx.lineWidth = 2;
        art_Art/* Art */.L.ctx.stroke();
       // console.log(this.normalizedMouseX, this.normalizedMouseY);
    }
    normalize(){
        this.normalizedMouseX = (this.mouseX / art_Art/* Art */.L.width) * 2 - 1;
        this.normalizedMouseY = (this.mouseY / art_Art/* Art */.L.height) * 2 - 1;
    }

    getTracking(){ 

        return [this.normalizedMouseX,this.normalizedMouseY];
    }

    settTracking(x,y){
        this.mouseX=x;
        this.mouseY=y;
    }
}
;// CONCATENATED MODULE: ./src/art/Tree.js







class Tree extends art_Drawable/* Drawable */.Q {
    constructor(point, grid_w, maxLevels = 4) {
        super();
        this.point = point;
        this.grid_w = grid_w;
        this.maxLevels =2// Math.min(maxLevels, 5); // Limit max levels 
        this.branches = [];
        this.trunkLength = grid_w * art_Art/* Art */.L.grid_dist * 1.5;
        
        this.grid_dist=art_Art/* Art */.L.grid_dist
        
        
        // Generate the tree structure
        this.generateTree();
    }
    
    generateTree() {
        // Create a perfectly straight trunk
        const trunk = new Path/* Path */.y();
        
        // Trunk points
        trunk.addPoint(this.point);
        trunk.addPoint(new art_Point/* Point */.E(this.point.x, this.point.y - this.trunkLength));
        
        // Store level and set style
        trunk.level = 0;
    
        // Add trunk to branches list
        this.branches.push(trunk);
        
        // Recursively generate branches
        this.generateBranchesForPath(trunk, 1);

        this.generatePolygons()
    }
    
    // Function to get a point along a path at a given fraction (0-1)
    getPointAlongPath(path, fraction) {
        // Ensure fraction is between 0 and 1
        fraction = Math.max(0, Math.min(1, fraction));
        
        // For two-point paths
        if (path.points.length === 2) {
            const startPoint = path.points[0];
            const endPoint = path.points[1];
            
            return new art_Point/* Point */.E(
                startPoint.x + (endPoint.x - startPoint.x) * fraction,
                startPoint.y + (endPoint.y - startPoint.y) * fraction
            );
        }
        
        // For multi-point paths
        const numSegments = path.points.length - 1;
        const totalDistance = this._getPathTotalLength(path);
        const targetDistance = totalDistance * fraction;
        
        let currentDistance = 0;
        
        for (let i = 0; i < numSegments; i++) {
            const start = path.points[i];
            const end = path.points[i + 1];
            
            const segmentLength = this._getDistanceBetweenPoints(start, end);
            
            if (currentDistance + segmentLength >= targetDistance) {
                // Target point is on this segment
                const segmentFraction = (targetDistance - currentDistance) / segmentLength;
                return new art_Point/* Point */.E(
                    start.x + (end.x - start.x) * segmentFraction,
                    start.y + (end.y - start.y) * segmentFraction
                );
            }
            
            currentDistance += segmentLength;
        }
        
        // Fallback to last point
        return path.points[path.points.length - 1];
    }
    
    // Helper to get total path length
    _getPathTotalLength(path) {
        let length = 0;
        for (let i = 0; i < path.points.length - 1; i++) {
            length += this._getDistanceBetweenPoints(path.points[i], path.points[i + 1]);
        }
        return length;
    }
    
    // Helper to get distance between two points
    _getDistanceBetweenPoints(p1, p2) {
        const dx = p2.x - p1.x;
        const dy = p2.y - p1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    generateBranchesForPath(parentPath, level) {
        // Stop recursion if maxLevels reached
        if (level > this.maxLevels) return;
        
        // Get parent endpoints to calculate direction
        const parentStart = parentPath.points[0];
        const parentEnd = parentPath.points[parentPath.points.length - 1];
        const parentDir = Math.atan2(parentEnd.y - parentStart.y, parentEnd.x - parentStart.x);
        
        // Branch length decreases with level
        //param?
        const branchLength = this.trunkLength * (0.5 / Math.sqrt(level));
        
        // Branch thickness decreases with level (more aggressively)
        const thickness = parentPath.style.lineWidth * 0.75;
        
        // Determine symmetric branch positions based on level
        let branchPositions = [];
        
        if (level === 1) {
            // First level branches at specific positions
          //  branchPositions = [0.5, 0.7, 0.9];
            //params
            branchPositions = [0.5];
        } else if (level === 2) {
            // Second level branches at regular intervals
            branchPositions = [0.3, 0.5,0.7];
            //branchPositions = [0.5];
        } else {
            // Higher levels just branch at the ends
            branchPositions = [0.2,0.9];
        }
        
        // For each branch position
        for (const position of branchPositions) {
            // Get the point along the parent path
            const branchStartPoint = this.getPointAlongPath(parentPath, position);
            
            // Calculate symmetric angles for left and right branches
            const baseAngle = parentDir; // Default to parent direction
            const angleOffset = Math.PI * 0.25; // 45 degrees
            
            // Create symmetric branches
            for (let side = -1; side <= 1; side += 2) { // -1 for left, +1 for right
                // Skip center for a cleaner look
                if (side === 0) continue;
                
                const angle = baseAngle + (angleOffset * side);
                const branchEnd = new art_Point/* Point */.E(
                    branchStartPoint.x + Math.cos(angle) * branchLength,
                    branchStartPoint.y + Math.sin(angle) * branchLength
                );
                
                // Create branch
                const branch = new Path/* Path */.y();
                branch.addPoint(branchStartPoint);
                branch.addPoint(branchEnd);
                
                // Set branch properties
                branch.level = level;
         
                
                // Add to branches collection
                this.branches.push(branch);
                
                // Recursively generate branches
                this.generateBranchesForPath(branch, level + 1);
            }
            
            // For trunk only, add a center branch continuing upward
            if (level === 1 && position === 0.9) {
                const upAngle = -Math.PI/2; // Straight up
                const upEnd = new art_Point/* Point */.E(
                    branchStartPoint.x + Math.cos(upAngle) * (branchLength * 0.7),
                    branchStartPoint.y + Math.sin(upAngle) * (branchLength * 0.7)
                );
                
                // Create upward branch
                const upBranch = new Path/* Path */.y();
                upBranch.addPoint(branchStartPoint);
                upBranch.addPoint(upEnd);
                
                // Set branch properties
                upBranch.level = level;
                upBranch.style = this.trunkStyle.clone();
                upBranch.style.lineWidth = thickness;
                
                // Add to branches collection
                this.branches.push(upBranch);
                
                // Recursively generate branches
                this.generateBranchesForPath(upBranch, level + 1);
            }
        }
    }
    
    generatePolygons(){
            // Performance: Pre-sort branches by level once
            if (!this.branchesSorted) {
                this.branches.sort((a, b) => a.level - b.level);
                this.branchesSorted = true;
            }

            this.polygons=[]

            let w=this.grid_dist*4*0.01
            // Draw branches
            for (const branch of this.branches) {
                let poly=Path/* Path */.y.extrude(branch,w,[1,0.5])
                poly.setStyle(
                    {
                        fillStyle: "#000000",
                        strokeStyle: "#ffffff",
                        lineWidth: 4*this.grid_dist*0.01
                    }
                )

                
             
                this.polygons.push(poly)
              
            }
        
            
    }


    paint() {
        this.polygons.forEach(p=>{
            p.stroke()
        })
        this.polygons.forEach(p=>{
            p.fill()
        })
    }
}
;// CONCATENATED MODULE: ./src/art/Sun.js





class Sun extends art_Drawable/* Drawable */.Q {
    constructor(x, y, size = null) {
        super();
        
        // Set default margin for the sun layer
        this.margin = 0.1;
        
        // Store position
        this.x = x;
        this.y = y;
        
        // Generate random size if not provided
        this.size = size || 150 + Tools.randInt(150);
        
        // Create the main sun circle
        this.sunCircle = new Ellipse(this.x, this.y, this.size, this.size);
        
        // Set default style
        this.sunCircle.setStyle({
            strokeStyle: "#",
            fillStyle: "#fff"
        });
        
        // Create a glow effect
        this.glowSize = this.size * 1.2;
        this.glowOpacity = 0.3;
        
        // Store time of creation for animation
        this.creationTime = Date.now();
    }
    
    /**
     * Paint the sun with glow effect
     */
    paint() {
        console.log("painting the sun");
        // Save current context state
        art_Art/* Art */.L.ctx.save();
        
        // Calculate animation values
        const time = (Date.now() - this.creationTime) / 1000; // Time in seconds
        const pulseFactor = 1 + Math.sin(time * 0.5) * 0.05; // Subtle pulsing effect
        
        // Draw outer glow
        const gradient = art_Art/* Art */.L.ctx.createRadialGradient(
            this.x, this.y, this.size * 0.5,
            this.x, this.y, this.glowSize * pulseFactor
        );
        
        gradient.addColorStop(0, 'rgba(255, 255, 200, ' + this.glowOpacity + ')');
        gradient.addColorStop(0.5, 'rgba(255, 200, 100, ' + (this.glowOpacity * 0.5) + ')');
        gradient.addColorStop(1, 'rgba(255, 150, 50, 0)');
        
        art_Art/* Art */.L.ctx.beginPath();
        art_Art/* Art */.L.ctx.arc(this.x, this.y, this.glowSize * pulseFactor, 0, Math.PI * 2);
        art_Art/* Art */.L.ctx.fillStyle = gradient;
        art_Art/* Art */.L.ctx.fill();
        
        // Draw sun rays
        this.drawSunRays(time);
        
        // Draw the main sun circle
        this.sunCircle.paint();
        
        // Restore context state
        art_Art/* Art */.L.ctx.restore();
    }
    
    /**
     * Draw animated sun rays
     * @param {number} time - Current time in seconds
     */
    drawSunRays(time) {
        const rayCount = 12;
        const rayLength = this.size * 0.5;
        const rayWidth = this.size * 0.05;
        
        art_Art/* Art */.L.ctx.save();
        art_Art/* Art */.L.ctx.translate(this.x, this.y);
        art_Art/* Art */.L.ctx.rotate(time * 0.1); // Slow rotation
        
        // Draw rays
        for (let i = 0; i < rayCount; i++) {
            const angle = (i / rayCount) * Math.PI * 2;
            const rayExtension = Math.sin(time * 0.5 + i) * 0.2; // Ray length variation
            
            art_Art/* Art */.L.ctx.save();
            art_Art/* Art */.L.ctx.rotate(angle);
            
            // Create ray gradient
            const rayGradient = art_Art/* Art */.L.ctx.createLinearGradient(
                this.size * 0.5, 0,
                this.size * 0.5 + rayLength * (1 + rayExtension), 0
            );
            rayGradient.addColorStop(0, 'rgba(255, 255, 200, 0.8)');
            rayGradient.addColorStop(1, 'rgba(255, 200, 100, 0)');
            
            // Draw ray
            art_Art/* Art */.L.ctx.beginPath();
            art_Art/* Art */.L.ctx.moveTo(this.size * 0.5, -rayWidth);
            art_Art/* Art */.L.ctx.lineTo(this.size * 0.5 + rayLength * (1 + rayExtension), 0);
            art_Art/* Art */.L.ctx.lineTo(this.size * 0.5, rayWidth);
            art_Art/* Art */.L.ctx.fillStyle = rayGradient;
            art_Art/* Art */.L.ctx.fill();
            
            art_Art/* Art */.L.ctx.restore();
        }
        
        art_Art/* Art */.L.ctx.restore();
    }
    
    /**
     * Get the bounds of the sun including its glow
     * @returns {Object} Bounds object with minX, minY, maxX, maxY, width, height
     */
    getBounds() {
        const totalSize = this.glowSize * 1.5; // Include rays in bounds calculation
        return {
            minX: this.x - totalSize,
            minY: this.y - totalSize,
            maxX: this.x + totalSize,
            maxY: this.y + totalSize,
            width: totalSize * 2,
            height: totalSize * 2
        };
    }
}

;// CONCATENATED MODULE: ./src/art/SunLayer.js




class SunLayer extends art_Drawable/* Drawable */.Q {
    constructor(width, height) {
        super();

        // Set a margin for the sun layer to allow for movement
        this.margin = 0.3;

        // Store canvas dimensions
        this.width = width;
        this.height = height;

        // Create the sun
        // Position the sun more centrally to avoid it moving off-screen
        const sunX = width*(0.5+0.5*art_Art/* Art */.L.rand()-0.25);
        const sunY = height*(0.25+0.25*art_Art/* Art */.L.rand()-0.125);
        this.sun = new Sun(sunX, sunY);

        // Store time of creation for animation
        this.creationTime = Date.now();
    }

    /**
     * Paint only the sun (no background)
     */
    paint() {
        // Save current context state
        art_Art/* Art */.L.ctx.save();

        // Draw the sun
        this.sun.paint();

        // Restore context state
        art_Art/* Art */.L.ctx.restore();
    }

    /**
     * Get the bounds of the sun layer
     * @returns {Object} Bounds object with minX, minY, maxX, maxY, width, height
     */
    getBounds() {
        // Use the sun's bounds plus some margin
        const sunBounds = this.sun.getBounds();
        
        // Add some margin around the sun's bounds
        return {
            minX: sunBounds.minX - this.width * 0.1,
            minY: sunBounds.minY - this.height * 0.1,
            maxX: sunBounds.maxX + this.width * 0.1,
            maxY: sunBounds.maxY + this.height * 0.1,
            width: sunBounds.width + this.width * 0.2,
            height: sunBounds.height + this.height * 0.2
        };
    }
}

;// CONCATENATED MODULE: ./src/art/names.js


// Original Greenlandic place names for reference and random inclusion
const realNames = [
    "Nuuk", "Ilulissat", "Sisimiut", "Qaqortoq", "Aasiaat",
    "Maniitsoq", "Tasiilaq", "Paamiut", "Uummannaq", "Narsaq",
    "Kulusuk", "Ittoqqortoormiit", "Qeqertarsuaq", "Kangerlussuaq", "Nanortalik",
    "Ikerasak", "Qaarsut", "Niaqornat", "Saqqaq", "Oqaatsut"
  ];
  
  // Common patterns and elements in Greenlandic names
  const prefixes = ["Qa", "Nu", "Ilu", "Si", "Ma", "Ta", "Pa", "Uu", "Na", "Ku", "Itte", "Qe", "Ka", "Ni", "Ike", "O", "Aa"];
  const middleParts = ["qo", "lu", "si", "ni", "ma", "taa", "paa", "uma", "noo", "kaa", "qeq", "nga"];
  const suffixes = ["q", "k", "t", "miut", "ssuaq", "rtoq", "liaq", "siaat", "rnat", "rsut", "aaq", "nnaq"];
  
  function generateGreenlandicName() {
    // 25% chance to return a real name from the list
    if (art_Art/* Art */.L.rand() < 0.25) {
      const randomIndex = Math.floor(art_Art/* Art */.L.rand() * realNames.length);
      return realNames[randomIndex];
    }
    
    // Otherwise generate a new name with similar patterns
    const prefix = prefixes[Math.floor(art_Art/* Art */.L.rand() * prefixes.length)];
    
    // 70% chance to include a middle part
    const includeMiddle = art_Art/* Art */.L.rand() < 0.7;
    const middle = includeMiddle ? middleParts[Math.floor(art_Art/* Art */.L.rand() * middleParts.length)] : "";
    
    // 85% chance to include a suffix
    const includeSuffix = art_Art/* Art */.L.rand() < 0.85;
    const suffix = includeSuffix ? suffixes[Math.floor(art_Art/* Art */.L.rand() * suffixes.length)] : "";
    
    // Ensure double letters don't appear too frequently
    let name = prefix + middle + suffix;
    
    // Capitalize only the first letter
    return name.charAt(0).toUpperCase() + name.slice(1);
  }
;// CONCATENATED MODULE: ./src/art/index.js























// EXTERNAL MODULE: ./node_modules/stats.js/build/stats.min.js
var stats_min = __webpack_require__(466);
var stats_min_default = /*#__PURE__*/__webpack_require__.n(stats_min);
;// CONCATENATED MODULE: ./index.js






const sp = new URLSearchParams(window.location.search)
//  console.log(sp);

// this is how to define parameters
$fx.params([
    {
        id: "house_density",
        name: "House density",
        type: "number",
        options: {
          min: 0.1,
          max: 1,
          step: 0.05,
        },
      },
      {
        id: "house_prob",
        name: "House probability",
        type: "number",
        options: {
          min: -0.5,
          max: 1,
          step: 0.05,
        },
      },
      {
        id: "mountain_slope",
        name: "Mountain slope",
        type: "select",
        //default: "pear",
        options: {
          options: ["64", "92", "128","1024"],
        },
      },
      {
        id: "contour_density",
        name: "Contour line density",
        type: "number",
        options: {
          min: 0.5,
          max: 2.0,
          step: 0.1,
        },
      },
      {
        id: "contour_noise_scale",
        name: "Contour noise scale",
        type: "number",
        options: {
          min: 0.0001,
          max: 0.01,
          step: 0.0001,
        },
      },
      {
        id: "sky_contour_opacity",
        name: "Sky contour opacity",
        type: "number",
        options: {
          min: 0.05,
          max: 0.5,
          step: 0.05,
        },
      },
      {
        id: "day_time",
        name: "Day time",
        type: "select",
        options: {
          options: ["morning", "day", "evening", "night"],
        },
      },
      {
        id: "color_mode",
        name: "Color mode",
        type: "select",
        options: {
          options: ["normal", "black", "white", "b&w","color"],
        },
      },
      {
        id: "fog",
        name: "Fog",
        type: "boolean",
     
      },

  {
    id: "prob_layer_occupation",
    name: "Prob. layer occupation",
    type: "number",
    //default: Math.PI,
    options: {
      min: 0.2,
      max: 1,
      step: 0.1,
    },
  },

  {
    id: "bigint_id",
    name: "A bigint",
    type: "bigint",
    update: "code-driven",
    //default: BigInt(Number.MAX_SAFE_INTEGER * 2),
    options: {
      min: Number.MIN_SAFE_INTEGER * 4,
      max: Number.MAX_SAFE_INTEGER * 4,
      step: 1,
    },
  },
  {
    id: "string_id_long",
    name: "A string long",
    type: "string",
    update: "code-driven",
    //default: "hello",
    options: {
      minLength: 1,
      maxLength: 512,
    },
  },
  {
    id: "select_id",
    name: "A selection",
    type: "select",
    update: "code-driven",
    //default: "pear",
    options: {
      options: ["apple", "orange", "pear"],
    },
  },
  {
    id: "color_id",
    name: "A color",
    type: "color",
    update: "code-driven",
    //default: "ff0000",
  },
  {
    id: "boolean_id",
    name: "A boolean",
    type: "boolean",
    update: "code-driven",
    //default: true,
  },
  {
    id: "string_id",
    name: "A string",
    type: "string",
    update: "code-driven",
    //default: "hello",
    options: {
      minLength: 1,
      maxLength: 512,
    },
  },
])

// this is how features can be defined
//todo posar aqui
$fx.features({
  "A random feature": Math.floor($fx.rand() * 10),
  "A random boolean": $fx.rand() > 0.5,
  "A random string": ["A", "B", "C", "D"].at(Math.floor($fx.rand() * 4)),
  "Day time": $fx.getParam("day_time"),
})


$fx.on(
    "params:update",
    newRawValues => {
      // opt-out default behaviour
      if (newRawValues.number_id === 5) return false
      // opt-in default behaviour
      return true
    },
    (optInDefault, newValues) => main()
  )




        const canvas = document.getElementById('canvas');

        // Base grid_dist value - used as a reference, but individual layers will have scaled values
        art_Art/* Art */.L.grid_dist=50*1

        art_Art/* Art */.L.width=1920
        art_Art/* Art */.L.height=1080



      //  const ANIMATION_SPEED=100


        let mycanvas=new Canvas(canvas)

       // mycanvas.setRatio(1)

        let tracking
        //todo fer millor

        let stats
        let city_name

        const config=new Config()
        config.load('./data/config.json').then(() => {
            console.log("tinc config",config)
            if(config.get("debug")){

                 stats = stats_min_default()()
                document.body.appendChild(stats.dom)
            }

            mycanvas.setRatio(config.get("ratio"))
            tracking=new Tracking(canvas,config.get("mode"))

            if(config.get("auto_refresh",0)!=0){
                setTimeout(() => {
                    window.location.reload();
                }, config.get("auto_refresh") * 1000);
            }
            city_name = generateGreenlandicName()


            // Update the city name in the HTML overlay
            document.getElementById('city-name').textContent = city_name;

            initApp();
        });


        function initApp(){

            const noise=new Noise()

            const scene = new Scene(canvas);

            // Get base sky colors for gradient (store as global variables)
            window.skyTopColor = art_Art/* Art */.L.palette.getRandColor("sky");
            // Create a middle color by slightly lightening the top color
            window.skyMiddleColor = art_Art/* Art */.L.palette.getColorLighterEx(window.skyTopColor, 0.15);
            // Create a bottom color by further lightening the top color
            window.skyBottomColor = art_Art/* Art */.L.palette.getColorLighterEx(window.skyTopColor, 0.3);

            // Create a dedicated sun layer (the farthest one)
            // We'll use a negative z-index to ensure it's always behind all other layers
            const sunLayer = new SunLayer(
                mycanvas.width,
                mycanvas.height
            );

            // Add the sun layer to the scene with z-index -1 (farthest)
            scene.add(sunLayer, -1);


        let land=new Landscape(mycanvas)
        let churchs=0
        let doms=0



        // Create multiple layers for better perspective effect
        land.createLayers()
            console.log("land",land)

        for(let i=0;i<land.layers.length-1;i++){
            churchs=0
            // Get layer-specific grid distance for properly scaled objects
            const layerGridDist = land.layers[i].grid_dist || art_Art/* Art */.L.grid_dist;


            // Track if any objects are added to this layer
            let elementsAddedToLayer = false;

            //console.log(land.layers[i])


            land.layers[i].setStyle(
                new Style/* Style */.b({fillStyle:art_Art/* Art */.L.palette.getRandColor("landscape")
            }));

            // Use consistent z-indexing that matches the visual depth
            // Back layers (index 0) have smaller grid_dist (25) and lower z-index
            // Front layers (higher indices) have larger grid_dist (100) for perspective
            scene.addLayer(land.layers[i],i);
            if(art_Art/* Art */.L.rand()<$fx.getParam("prob_layer_occupation") ){  //i=0 es l'ultim
            land.registerDrawables(i,function(p){

                let n=noise.noise2D(p.x,p.y,0.0001*layerGridDist);
                if(!p.visible){
                    //no existeixen)
                }else{
                    // Set temporary grid_dist to the layer value for proper scaling of objects
                    const originalGridDist = art_Art/* Art */.L.grid_dist;
                    art_Art/* Art */.L.grid_dist = layerGridDist;

                    function getObjectWidth(num){
                        if(num >= 2){
                            if(num>6) num=6
                            return Tools.randPattern({2:0.5,3:0.5,4:0.2,5:0.2,6:0.05},num)
                        }else{
                            return 0;
                        }
                    }
                    function markOccupied(p,num){
                        for(let i=0; i<num; i++){
                            p.nextPoints[i].type = "ncasa";
                        }
                    }

                    if(art_Art/* Art */.L.rand()<$fx.getParam("house_density") && n<$fx.getParam("house_prob") && p.type==undefined){  //n param
                        const num=getObjectWidth(p.nextPoints.length)

                        markOccupied(p,num)

                        var house
                        if(art_Art/* Art */.L.rand()<0.8){ // Increased probability of churches (20% instead of 1%)
                         // Pass the layer index (i) to adjust saturation based on distance
                         // Reverse the index so that 0 is furthest and higher numbers are closer
                         // This makes the furthest layers (higher i) more desaturated
                         const layerIndex = land.layers.length - 1 - i;
                         house = SwedishHouse.getHouse(num, p, layerIndex);

                        scene.add(house, i);
                        }else{


                            if(num>4 && doms<1 && art_Art/* Art */.L.rand()<1){
                                house = new SwedishDom(
                                    p,
                                );
                                house.layerIndex = land.layers.length - 1 - i;
                                doms++

                                scene.add(house, i);
                            }else if(num>2 && churchs<4){
                                house = new SwedishChurch(
                                    p,
                                    num
                                );
                                // Also store the layer index for churches
                                house.layerIndex = land.layers.length - 1 - i;
                                churchs++

                             scene.add(house, i);
                            }
                        }

                    }

                    if(art_Art/* Art */.L.rand()<0.2 ){
                        snow=new LandscapeSnow(
                            p,
                            1
                        );
                        scene.add(snow,i);
                    }


                if(art_Art/* Art */.L.rand()<0.5){
                    if(p.nextPoints.length>1){
                        /*let snow=new LandscapeSnow(
                            p,
                            1
                        );*/
                        var snow
                        if(art_Art/* Art */.L.rand()<0.5){
                             snow=new LandscapeSnow(
                                p,
                                1
                            );
                        }else{
                                snow=new Tree(
                                p,
                                1
                            );
                    }

                       // scene.add(snow,i);

                    }
                }




                // Restore the original grid_dist after creating objects for this point
                art_Art/* Art */.L.grid_dist = originalGridDist;
            }

            });


        }

        }

        // Create all layer bitmaps in advance for better performance
        scene.createLayerBitmaps();
        console.log("scene",scene)
        let xx = 0; // Animation time variable
        let targetX = 0; // Target position for parallax effect
        let targetY = 0;



        setInterval(() => {
            // Recreate the layer bitmaps to update the clock
            updateBitmaps();
        }, 40*5*1); // Update every minute (60000 ms)

        function updateBitmaps() {
            // Get the city name overlay element
            const cityNameOverlay = document.getElementById('city-name-overlay');

            if (!scene.allPainted()) {
                // If not all painted, recreate the layer bitmaps
                scene.createLayerBitmaps();

                // Make sure the city name is in the center position
                if (cityNameOverlay && cityNameOverlay.classList.contains('corner-position')) {
                    cityNameOverlay.classList.remove('corner-position');
                }
            } else {
                // All painted - move the city name to the bottom right corner
                if (cityNameOverlay && !cityNameOverlay.classList.contains('corner-position')) {
                    cityNameOverlay.classList.add('corner-position');

                    // Ensure the city name is fully visible in the bottom right corner
                    const cityNameElement = document.getElementById('city-name');
                    if (cityNameElement) {
                        // Make sure the text is visible by ensuring it's not too long
                        if (city_name.length > 15) {
                            // If the name is too long, truncate it to ensure visibility
                            cityNameElement.textContent = city_name.substring(0, 15);
                        }
                    }

                    console.log('City name moved to corner position');
                }
            }
        }



        animate()


       // land.debug()

        function animate(){
            // Draw the sky gradient directly on the canvas
            // Create a linear gradient for the sky
            const skyGradient = art_Art/* Art */.L.ctx.createLinearGradient(0, 0, 0, art_Art/* Art */.L.height);
            skyGradient.addColorStop(0, window.skyTopColor); // Top color
            skyGradient.addColorStop(0.5, window.skyMiddleColor); // Middle color
            skyGradient.addColorStop(1, window.skyBottomColor); // Bottom color

            // Apply the gradient as fill style
            art_Art/* Art */.L.ctx.fillStyle = skyGradient;

            // Fill the entire canvas with the gradient
            art_Art/* Art */.L.fillRect(0, 0, art_Art/* Art */.L.width, art_Art/* Art */.L.height);

            //valors entre -1 i 1
            let tracking_values = tracking.getTracking()
            targetX = tracking_values[0]
            targetY = tracking_values[1]

            // Get parallax configuration values (or use defaults if not available)
            const parallaxConfig = config.get("parallax") || {};
            const parallaxStrength = parallaxConfig.strength || 100; // Default: 100
            const exaggeration = parallaxConfig.exaggeration || 1.0; // Default: 1.0 (no exaggeration)
            const waveAmplitude = parallaxConfig.waveAmplitude || 5; // Default: 5
            const waveFrequency = parallaxConfig.waveFrequency || 8; // Default: 8

            // Draw the sun layer (z-index -1) with minimal parallax effect
            // The sun should move very slightly to create a distant effect
            const sunParallaxFactor = 0.08; // Very small factor for distant sun

            // Calculate the maximum allowed movement based on the sun layer margin
            const sunLayer = scene.layers.find(layer => layer.zIndex === -1);
            const sunMargin = sunLayer ? sunLayer.margin || 0.3 : 0.3;

            // Calculate maximum allowed movement (80% of the margin to be safe)
            const maxSunMoveX = art_Art/* Art */.L.width * sunMargin * 0.8;
            const maxSunMoveY = art_Art/* Art */.L.height * sunMargin * 0.8;

            // Calculate initial position with parallax effect
            let sunPosX = -targetX * parallaxStrength * sunParallaxFactor;
            let sunPosY = -targetY * parallaxStrength * sunParallaxFactor;

            // Add a very subtle wave movement to the sun
            const sunWaveX = Math.sin(xx * waveFrequency * 0.5) * waveAmplitude * 0.1;
            const sunWaveY = Math.cos(xx * waveFrequency * 0.3) * waveAmplitude * 0.05;

            // Combine effects for sun position
            sunPosX += sunWaveX;
            sunPosY += sunWaveY;

            // Clamp the movement to stay within the safe bounds
            sunPosX = Math.max(Math.min(sunPosX, maxSunMoveX), -maxSunMoveX);
            sunPosY = Math.max(Math.min(sunPosY, maxSunMoveY), -maxSunMoveY);

            // Draw the sun layer
            scene.paintLayerBitmap(-1, sunPosX, sunPosY);

            // Draw each landscape layer with parallax effect
            for(let i=0; i<land.layers.length; i++){
                // We're reusing the parallax configuration values from above

                // Calculate layer depth factor - more exaggerated difference between layers
                // Use exponential function to create more dramatic difference between layers
                // Nearest layers (higher index) will move much more than distant layers
                const layerCount = land.layers.length;
                const normalizedIndex = i / (layerCount - 1); // 0 to 1
                const layerDepthFactor = Math.pow(normalizedIndex * exaggeration + 0.5, 2) * layerCount;

                // Calculate positions with increased strength:
                // - Inverse targetX because we want layers to move opposite to mouse direction
                // - Apply exponential depth factor for more dramatic effect

                let parallaxX = -targetX * parallaxStrength * (layerDepthFactor / layerCount);
                let parallaxY = -targetY * parallaxStrength * (layerDepthFactor / layerCount);


                // Add automatic wave movement with configurable amplitude and frequency
                // Distant layers (low index) have subtle movement, close layers have more pronounced movement
                const layerWaveFactor = 1 + (i / layerCount) * 2; // 1.0 to 3.0
                const waveX = Math.sin(xx * waveFrequency) * waveAmplitude * layerWaveFactor / (layerCount - i || 1);
                const waveY = Math.cos(xx * (waveFrequency * 0.6)) * (waveAmplitude * 0.6) * layerWaveFactor / (layerCount - i || 1);

                // Combine parallax and wave effects
                let posX = parallaxX + waveX;
                let posY = parallaxY + waveY;

                // Get layer dimensions to keep within canvas bounds
                const layer = land.layers[i];
                const margin = layer && layer.margin ? layer.margin : 0;

                // Calculate max allowed movement for parallax
                // The margin defines how much extra space we have around the content
                // This space can be used for movement while keeping content visible
                const maxOffsetX = margin * art_Art/* Art */.L.width * art_Art/* Art */.L.ratio / 2;
                const maxOffsetY = margin * art_Art/* Art */.L.height * art_Art/* Art */.L.ratio / 2;

                // Clamp positions to keep content visible within canvas bounds
                posX = Math.max(Math.min(posX, maxOffsetX), -maxOffsetX);
                posY = Math.max(Math.min(posY, maxOffsetY), -maxOffsetY);

                // Layer bitmap drawing handles the margin offset internally
                scene.paintLayerBitmap(i, posX, posY);
            }

            // Update animation variables
            xx += 0.002;
            if(config.get("debug")){
                tracking.debug();
            }
            // Continue animation loop
            requestAnimationFrame(animate);

            if(config.get("debug")) stats.update()
        }

    }
   

})();

/******/ })()
;